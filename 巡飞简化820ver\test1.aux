\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand*\HyPL@Entry[1]{}
\gdef \LT@i {\LT@entry 
    {1}{184.02411pt}\LT@entry 
    {1}{160.97589pt}}
\HyPL@Entry{0<</S/D>>}
\@writefile{toc}{\contentsline {subsection}{\textbf  {1 运动学模型与任务场景描述}}{5}{subsection*.1}\protected@file@percent }
\newlabel{ux8fd0ux52a8ux5b66ux6a21ux578bux4e0eux4efbux52a1ux573aux666fux63cfux8ff0}{{}{5}{\texorpdfstring {\textbf {1 运动学模型与任务场景描述}}{1 运动学模型与任务场景描述}}{subsection*.1}{}}
\@writefile{toc}{\contentsline {subsection}{\textbf  {2 DWA-RL融合架构设计}}{7}{subsection*.2}\protected@file@percent }
\newlabel{dwa-rlux878dux5408ux67b6ux6784ux8bbeux8ba1}{{}{7}{\texorpdfstring {\textbf {2 DWA-RL融合架构设计}}{2 DWA-RL融合架构设计}}{subsection*.2}{}}
\@writefile{toc}{\contentsline {subsection}{\textbf  {3 基于CPO框架的巡飞弹运动规划算法}}{8}{subsection*.3}\protected@file@percent }
\newlabel{ux57faux4e8ecpoux6846ux67b6ux7684ux5de1ux98deux5f39ux8fd0ux52a8ux89c4ux5212ux7b97ux6cd5}{{}{8}{\texorpdfstring {\textbf {3 基于CPO框架的巡飞弹运动规划算法}}{3 基于CPO框架的巡飞弹运动规划算法}}{subsection*.3}{}}
\@writefile{toc}{\contentsline {subsection}{\textbf  {4 仿真训练与结果分析}}{14}{subsection*.4}\protected@file@percent }
\newlabel{ux4effux771fux8badux7ec3ux4e0eux7ed3ux679cux5206ux6790}{{}{14}{\texorpdfstring {\textbf {4 仿真训练与结果分析}}{4 仿真训练与结果分析}}{subsection*.4}{}}
\@writefile{lot}{\contentsline {table}{\numberline {2}{\ignorespaces 巡飞弹运动学约束参数}}{16}{table.2}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {3}{\ignorespaces 仿真环境配置参数}}{16}{table.3}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {4}{\ignorespaces 静态障碍物参数设置}}{16}{table.4}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {5}{\ignorespaces 动态障碍物运动模式参数}}{17}{table.5}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {6}{\ignorespaces 典型测试场景设计}}{18}{table.6}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {7}{\ignorespaces DWA算法参数设置}}{19}{table.7}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {8}{\ignorespaces TD3神经网络参数设置}}{19}{table.8}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {9}{\ignorespaces 分阶段训练参数设置}}{20}{table.9}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {10}{\ignorespaces 算法收敛性能比较}}{21}{table.10}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {11}{\ignorespaces 不同环境下算法性能对比}}{22}{table.11}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {12}{\ignorespaces 消融实验结果}}{22}{table.12}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {13}{\ignorespaces 计算效率对比分析}}{22}{table.13}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\textbf  {5 结论}}{23}{subsection*.5}\protected@file@percent }
\newlabel{ux7ed3ux8bba}{{}{23}{\texorpdfstring {\textbf {5 结论}}{5 结论}}{subsection*.5}{}}
\gdef \@abspage@last{26}
