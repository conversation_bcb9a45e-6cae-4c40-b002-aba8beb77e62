This is XeTeX, Version 3.141592653-2.6-0.999996 (TeX Live 2024) (preloaded format=xelatex 2024.11.28)  21 AUG 2025 18:49
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**algorithm_pseudocode_compact.tex
(./algorithm_pseudocode_compact.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(e:/texlive/2024/texmf-dist/tex/latex/ctex/ctexart.cls
(e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctexbackend.cfg
File: ctexbackend.cfg 2022/07/14 v2.5.10 Backend configuration file (CTEX)
)
(e:/texlive/2024/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 

(e:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-02-20 L3 backend support: XeTeX
\g__graphics_track_int=\count184
\l__pdf_internal_box=\box51
\g__pdf_backend_object_int=\count185
\g__pdf_backend_annotation_int=\count186
\g__pdf_backend_link_int=\count187
))
Document Class: ctexart 2022/07/14 v2.5.10 Chinese adapter for class article (C
TEX)
(e:/texlive/2024/texmf-dist/tex/latex/ctex/ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
)
(e:/texlive/2024/texmf-dist/tex/latex/ctex/ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
)
(e:/texlive/2024/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX

(e:/texlive/2024/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\l__ctex_tmp_int=\count188
\l__ctex_tmp_box=\box52
\l__ctex_tmp_dim=\dimen140
\g__ctex_section_depth_int=\count189
\g__ctex_font_size_int=\count190

(e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctexopts.cfg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
)
(e:/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(e:/texlive/2024/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count191
\c@section=\count192
\c@subsection=\count193
\c@subsubsection=\count194
\c@paragraph=\count195
\c@subparagraph=\count196
\c@figure=\count197
\c@table=\count198
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen141
)
(e:/texlive/2024/texmf-dist/tex/latex/ctex/engine/ctex-engine-xetex.def
File: ctex-engine-xetex.def 2022/07/14 v2.5.10 XeLaTeX adapter (CTEX)

(e:/texlive/2024/texmf-dist/tex/xelatex/xecjk/xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX

(e:/texlive/2024/texmf-dist/tex/latex/l3packages/xtemplate/xtemplate.sty
(e:/texlive/2024/texmf-dist/tex/latex/l3packages/xtemplate/xtemplate-2023-10-10
.sty
Package: xtemplate 2023-10-10 L3 Experimental prototype document functions
\l__xtemplate_tmp_dim=\dimen142
\l__xtemplate_tmp_int=\count199
\l__xtemplate_tmp_muskip=\muskip16
\l__xtemplate_tmp_skip=\skip50
))
\l__xeCJK_tmp_int=\count266
\l__xeCJK_tmp_box=\box53
\l__xeCJK_tmp_dim=\dimen143
\l__xeCJK_tmp_skip=\skip51
\g__xeCJK_space_factor_int=\count267
\l__xeCJK_begin_int=\count268
\l__xeCJK_end_int=\count269
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip52
\c__xeCJK_none_node=\count270
\g__xeCJK_node_int=\count271
\c__xeCJK_CJK_node_dim=\dimen144
\c__xeCJK_CJK-space_node_dim=\dimen145
\c__xeCJK_default_node_dim=\dimen146
\c__xeCJK_CJK-widow_node_dim=\dimen147
\c__xeCJK_normalspace_node_dim=\dimen148
\c__xeCJK_default-space_node_skip=\skip53
\l__xeCJK_ccglue_skip=\skip54
\l__xeCJK_ecglue_skip=\skip55
\l__xeCJK_punct_kern_skip=\skip56
\l__xeCJK_indent_box=\box54
\l__xeCJK_last_penalty_int=\count272
\l__xeCJK_last_bound_dim=\dimen149
\l__xeCJK_last_kern_dim=\dimen150
\l__xeCJK_widow_penalty_int=\count273

Package xtemplate Info: Declaring object type 'xeCJK/punctuation' taking 0
(xtemplate)             argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen151
\l__xeCJK_mixed_punct_width_dim=\dimen152
\l__xeCJK_middle_punct_width_dim=\dimen153
\l__xeCJK_fixed_margin_width_dim=\dimen154
\l__xeCJK_mixed_margin_width_dim=\dimen155
\l__xeCJK_middle_margin_width_dim=\dimen156
\l__xeCJK_bound_punct_width_dim=\dimen157
\l__xeCJK_bound_margin_width_dim=\dimen158
\l__xeCJK_margin_minimum_dim=\dimen159
\l__xeCJK_kerning_total_width_dim=\dimen160
\l__xeCJK_same_align_margin_dim=\dimen161
\l__xeCJK_different_align_margin_dim=\dimen162
\l__xeCJK_kerning_margin_width_dim=\dimen163
\l__xeCJK_kerning_margin_minimum_dim=\dimen164
\l__xeCJK_bound_dim=\dimen165
\l__xeCJK_reverse_bound_dim=\dimen166
\l__xeCJK_margin_dim=\dimen167
\l__xeCJK_minimum_bound_dim=\dimen168
\l__xeCJK_kerning_margin_dim=\dimen169
\g__xeCJK_family_int=\count274
\l__xeCJK_fam_int=\count275
\g__xeCJK_fam_allocation_int=\count276
\l__xeCJK_verb_case_int=\count277
\l__xeCJK_verb_exspace_skip=\skip57
 (e:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.sty
(e:/texlive/2024/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-02-18 L3 Experimental document command parser
)
Package: fontspec 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTeX

(e:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTe
X
\l__fontspec_script_int=\count278
\l__fontspec_language_int=\count279
\l__fontspec_strnum_int=\count280
\l__fontspec_tmp_int=\count281
\l__fontspec_tmpa_int=\count282
\l__fontspec_tmpb_int=\count283
\l__fontspec_tmpc_int=\count284
\l__fontspec_em_int=\count285
\l__fontspec_emdef_int=\count286
\l__fontspec_strong_int=\count287
\l__fontspec_strongdef_int=\count288
\l__fontspec_tmpa_dim=\dimen170
\l__fontspec_tmpb_dim=\dimen171
\l__fontspec_tmpc_dim=\dimen172

(e:/texlive/2024/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(e:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.cfg)))
(e:/texlive/2024/texmf-dist/tex/xelatex/xecjk/xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
))
\ccwd=\dimen173
\l__ctex_ccglue_skip=\skip58
)
\l__ctex_ziju_dim=\dimen174

(e:/texlive/2024/texmf-dist/tex/latex/zhnumber/zhnumber.sty
Package: zhnumber 2022/07/14 v3.0 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count289
\l__zhnum_tmp_int=\count290

(e:/texlive/2024/texmf-dist/tex/latex/zhnumber/zhnumber-utf8.cfg
File: zhnumber-utf8.cfg 2022/07/14 v3.0 Chinese numerals with UTF8 encoding
))
\l__ctex_heading_skip=\skip59

(e:/texlive/2024/texmf-dist/tex/latex/ctex/scheme/ctex-scheme-chinese-article.d
ef
File: ctex-scheme-chinese-article.def 2022/07/14 v2.5.10 Chinese scheme for art
icle (CTEX)
 (e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctex-name-utf8.cfg
File: ctex-name-utf8.cfg 2022/07/14 v2.5.10 Caption with encoding UTF-8 (CTEX)
))
(e:/texlive/2024/texmf-dist/tex/latex/ctex/fontset/ctex-fontset-windows.def
File: ctex-fontset-windows.def 2022/07/14 v2.5.10 Windows fonts definition (CTE
X)

Package fontspec Info: Could not resolve font "KaiTi/B" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimHei/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimSun/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Font family 'SimSun(0)' created for font 'SimSun' with
(fontspec)             options
(fontspec)             [Script={CJK},BoldFont={SimHei},ItalicFont={KaiTi}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"SimSun/OT:script=hani;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"SimHei/OT:script=hani;language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"KaiTi/OT:script=hani;language=dflt;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.: 

))
(e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctex.cfg
File: ctex.cfg 2022/07/14 v2.5.10 Configuration file (CTEX)
)

Package xeCJK Warning: Redefining CJKfamily `\CJKrmdefault' (SimSun(0)).


Package fontspec Info: Could not resolve font "SimSun/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimSun/B" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimSun/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Font family 'SimSun(1)' created for font 'SimSun' with
(fontspec)             options [Script={CJK}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"SimSun/OT:script=hani;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 


Package xeCJK Warning: Redefining CJKfamily `\CJKsfdefault' (Microsoft YaHei).

(e:/texlive/2024/texmf-dist/tex/latex/algorithms/algorithm.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating enviro
nment

(e:/texlive/2024/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count291
\float@exts=\toks17
\float@box=\box55
\@float@everytoks=\toks18
\@floatcapt=\box56
)
(e:/texlive/2024/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
\@float@every@algorithm=\toks19
\c@algorithm=\count292
)
(e:/texlive/2024/texmf-dist/tex/latex/algorithmicx/algpseudocode.sty
Package: algpseudocode 

(e:/texlive/2024/texmf-dist/tex/latex/algorithmicx/algorithmicx.sty
Package: algorithmicx 2005/04/27 v1.2 Algorithmicx

Document Style algorithmicx 1.2 - a greatly improved `algorithmic' style
\c@ALG@line=\count293
\c@ALG@rem=\count294
\c@ALG@nested=\count295
\ALG@tlm=\skip60
\ALG@thistlm=\skip61
\c@ALG@Lnr=\count296
\c@ALG@blocknr=\count297
\c@ALG@storecount=\count298
\c@ALG@tmpcounter=\count299
\ALG@tmplength=\skip62
)
Document Style - pseudocode environments for use with the `algorithmicx' style
) (e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip63

For additional information on amsmath, use the `?' option.
(e:/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks20
\ex@=\dimen175
))
(e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen176
)
(e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count300
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count301
\leftroot@=\count302
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count303
\DOTSCASE@=\count304
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box57
\strutbox@=\box58
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen177
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count305
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count306
\dotsspace@=\muskip17
\c@parentequation=\count307
\dspbrk@lvl=\count308
\tag@help=\toks21
\row@=\count309
\column@=\count310
\maxfields@=\count311
\andhelp@=\toks22
\eqnshift@=\dimen178
\alignsep@=\dimen179
\tagshift@=\dimen180
\tagwidth@=\dimen181
\totwidth@=\dimen182
\lineht@=\dimen183
\@envbody=\toks23
\multlinegap=\skip64
\multlinetaggap=\skip65
\mathdisplay@stack=\toks24
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(e:/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(e:/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(e:/texlive/2024/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(e:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks25
)
(e:/texlive/2024/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(e:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count312
\Gm@cntv=\count313
\c@Gm@tempcnt=\count314
\Gm@bindingoffset=\dimen184
\Gm@wd@mp=\dimen185
\Gm@odd@mp=\dimen186
\Gm@even@mp=\dimen187
\Gm@layoutwidth=\dimen188
\Gm@layoutheight=\dimen189
\Gm@layouthoffset=\dimen190
\Gm@layoutvoffset=\dimen191
\Gm@dimlist=\toks26
)
No file algorithm_pseudocode_compact.aux.
\openout1 = `algorithm_pseudocode_compact.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.

Package fontspec Info: Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 30.
LaTeX Font Info:    Redeclaring math accent \acute on input line 30.
LaTeX Font Info:    Redeclaring math accent \grave on input line 30.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 30.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 30.
LaTeX Font Info:    Redeclaring math accent \bar on input line 30.
LaTeX Font Info:    Redeclaring math accent \breve on input line 30.
LaTeX Font Info:    Redeclaring math accent \check on input line 30.
LaTeX Font Info:    Redeclaring math accent \hat on input line 30.
LaTeX Font Info:    Redeclaring math accent \dot on input line 30.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 30.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 30.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 30.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 30.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 30.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 30.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 30.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/lmr/m/n on input line 30.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 30.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 30.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/b/n on input line 30.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 30.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 30.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 30.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/lmr/b/it on input line 30.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 30.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 30.
*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(56.9055pt, 731.23584pt, 56.9055pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=731.23584pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-52.36449pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=59.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)


LaTeX Font Warning: Font shape `TU/SimSun(1)/b/n' undefined
(Font)              using `TU/SimSun(1)/m/n' instead on input line 32.

LaTeX Font Info:    Trying to load font information for U+msa on input line 32.

(e:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 32.


(e:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
) [1

]

LaTeX Font Warning: Font shape `TU/SimSun(1)/m/sl' undefined
(Font)              using `TU/SimSun(1)/m/n' instead on input line 90.

[2] (./algorithm_pseudocode_compact.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2022/07/14>
 ***********


LaTeX Font Warning: Some font shapes were not available, defaults substituted.


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.

 ) 
Here is how much of TeX's memory you used:
 9419 strings out of 474773
 245969 string characters out of 5761288
 1937842 words of memory out of 5000000
 31458 multiletter control sequences out of 15000+600000
 566112 words of font info for 88 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 93i,6n,97p,367b,434s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on algorithm_pseudocode_compact.pdf (2 pages).
