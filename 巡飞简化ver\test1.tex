% Options for packages loaded elsewhere
\PassOptionsToPackage{unicode}{hyperref}
\PassOptionsToPackage{hyphens}{url}
\documentclass[UTF8]{ctexart}

% 字体设置
\usepackage{fontspec}
\usepackage{xeCJK}
\setCJKmainfont{SimSun}
\setCJKsansfont{SimHei}
% 字体命令由ctexart自动提供

% 表格相关包
\usepackage{multirow}
\usepackage{array}
\usepackage{xcolor}
\usepackage{amsmath,amssymb}
% 算法相关包
\usepackage{algorithm}
\usepackage{algpseudocode}
\setcounter{secnumdepth}{-\maxdimen} % remove section numbering
\usepackage{iftex}
\ifPDFTeX
  \usepackage[T1]{fontenc}
  \usepackage[utf8]{inputenc}
  \usepackage{textcomp} % provide euro and other symbols
\else % if luatex or xetex
  \usepackage{unicode-math} % this also loads fontspec
  \defaultfontfeatures{Scale=MatchLowercase}
  \defaultfontfeatures[\rmfamily]{Ligatures=TeX,Scale=1}
\fi
\usepackage{lmodern}
\ifPDFTeX\else
  % xetex/luatex font selection
\fi
% Use upquote if available, for straight quotes in verbatim environments
\IfFileExists{upquote.sty}{\usepackage{upquote}}{}
\IfFileExists{microtype.sty}{% use microtype if available
  \usepackage[]{microtype}
  \UseMicrotypeSet[protrusion]{basicmath} % disable protrusion for tt fonts
}{}
\makeatletter
\@ifundefined{KOMAClassName}{% if non-KOMA class
  \IfFileExists{parskip.sty}{%
    \usepackage{parskip}
  }{% else
    \setlength{\parindent}{0pt}
    \setlength{\parskip}{6pt plus 2pt minus 1pt}}
}{% if KOMA class
  \KOMAoptions{parskip=half}}
\makeatother
\usepackage{longtable,booktabs,array}
\usepackage{calc} % for calculating minipage widths
% Correct order of tables after \paragraph or \subparagraph
\usepackage{etoolbox}
\makeatletter
\patchcmd\longtable{\par}{\if@noskipsec\mbox{}\fi\par}{}{}
\makeatother
% Allow footnotes in longtable head/foot
\IfFileExists{footnotehyper.sty}{\usepackage{footnotehyper}}{\usepackage{footnote}}
\makesavenoteenv{longtable}
\usepackage{graphicx}
\makeatletter
\newsavebox\pandoc@box
\newcommand*\pandocbounded[1]{% scales image to fit in text height/width
  \sbox\pandoc@box{#1}%
  \Gscale@div\@tempa{\textheight}{\dimexpr\ht\pandoc@box+\dp\pandoc@box\relax}%
  \Gscale@div\@tempb{\linewidth}{\wd\pandoc@box}%
  \ifdim\@tempb\p@<\@tempa\p@\let\@tempa\@tempb\fi% select the smaller of both
  \ifdim\@tempa\p@<\p@\scalebox{\@tempa}{\usebox\pandoc@box}%
  \else\usebox{\pandoc@box}%
  \fi%
}
% Set default figure placement to htbp
\def\fps@figure{htbp}
\makeatother
\ifLuaTeX
  \usepackage{luacolor}
  \usepackage[soul]{lua-ul}
\else
  \usepackage{soul}
\fi
\setlength{\emergencystretch}{3em} % prevent overfull lines
\providecommand{\tightlist}{%
  \setlength{\itemsep}{0pt}\setlength{\parskip}{0pt}}
\usepackage{bookmark}
\IfFileExists{xurl.sty}{\usepackage{xurl}}{} % add URL line breaks if available
\urlstyle{same}
\hypersetup{
  hidelinks,
  pdfcreator={LaTeX via pandoc}}

\author{}
\date{}

\begin{document}

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\linewidth - 2\tabcolsep) * \real{0.5346}}
  >{\raggedright\arraybackslash}p{(\linewidth - 2\tabcolsep) * \real{0.4654}}@{}}
\toprule\noalign{}
\begin{minipage}[b]{\linewidth}\raggedright
\textbf{DOI：} https://doi.org/
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedleft
\end{minipage} \\
\midrule\noalign{}
\endhead
\bottomrule\noalign{}
\endlastfoot
\end{longtable}

\begin{quote}
\textbf{融合动态安全窗口与约束策略优化的}
\end{quote}

\textbf{巡飞弹运动规划算法}\footnote{\textbf{收稿日期：}2025-

  \textbf{基金项目：}

  \textbf{*通信作者邮箱：}}

\textbf{摘要：}针对巡飞弹在复杂动态环境中需兼顾路径效率、运动学性能与多重安全约束的挑战，提出一种协同运动规划框架。该方法以约束策略优化为全局策略优化基础，将改进动态窗口法作为实时安全动作筛选器，确保产生严格满足巡飞弹运动学约束并有效规避障碍物的安全动作集。智能体在安全动作空间内通过多目标加权的自适应评价函数优化长期累积奖励，有效提升全局路径效率与能耗经济性。训练阶段引入分阶段课程学习机制，从简单静态环境逐步过渡至密集动态环境，显著提升算法收敛速度与环境适应性。仿真实验表明，本算法可实现局部安全动作精准筛选与全局策略高效学习的协同优化，在维持路径效率的同时，考虑运动学性能指标与环境交互安全性，为巡飞弹在复杂动态场景下的可靠自主规划提供了有效解决方案。

\begin{quote}
\textbf{关键词：}巡飞弹,
约束策略优化，改进动态窗口法，强化学习，安全动作集，运动规划

\textbf{中图分类号：} \textbf{文献标志码：}A \textbf{文章编号：}
\end{quote}

\textbf{Loitering Munition Motion Planning Algorithm Integrating Dynamic
Safety Window and Constrained Policy Optimization}

\textbf{Abstract:} To address the challenge of loitering munitions
navigating complex dynamic environments while balancing path efficiency,
kinematic performance, and multiple safety constraints, this paper
proposes a collaborative motion planning framework. The method employs
Constrained Policy Optimization as the foundation for global policy
optimization, integrating an enhanced Dynamic Window Approach as a
real-time safety action filter. This ensures the generation of action
sets that strictly satisfy kinematic constraints and effectively avoid
obstacles. Within this safe action space, the agent optimizes long-term
cumulative rewards through a multi-objective weighted adaptive
evaluation function, significantly improving global path efficiency and
energy economy. A phased curriculum learning mechanism is introduced
during training, progressively transitioning from simple static
environments to dense dynamic scenarios, markedly enhancing algorithm
convergence speed and environmental adaptability. Simulation experiments
demonstrate that the proposed algorithm achieves synergistic
optimization of precise local safety action filtering and efficient
global policy learning. It maintains path efficiency while incorporating
kinematic performance metrics and environmental interaction safety,
providing an effective solution for reliable autonomous planning of
loitering munitions in complex dynamic scenarios.

\textbf{Keywords:} Loitering munition; Constrained policy optimization;
Enhanced dynamic window approach ; Reinforcement learning; Safe action
set; Motion planning

\textbf{0 引言}

随着多域协同集群攻防作战模式研究的深化以及当前局部冲突的实战化体现，巡飞弹作为一种集长时滞空、主动侦察与精确打击于一体的智能化无人作战装备，已在现代战场中占据关键地位{[}1−3{]}。尤其在复杂城市环境或集群对抗场景中，巡飞弹需面对多重挑战，包括移动障碍或动态威胁区域实时规避、严苛的运动学约束以及高效完成任务目标。这些需求对运动规划算法的路径效率、安全性与实时性提出了极高要求。然而，传统运动规划方法在兼顾全局路径优化与局部动态响应时表现一般，成为制约巡飞弹自主作战效能提升的瓶颈问题{[}2−4{]}。在此背景下，亟需探索能够融合全局优化与局部实时安全响应能力的新型规划框架。

在巡飞弹运动规划领域，传统方法主要分为几何路径规划方法与运动学跟踪控制两大类。几何路径规划方法，如A*算法、快速扩展随机树RRT及其变种侧重于在构型空间（C-space）中搜索连接起点与目标点的无碰撞路径。这类方法通过离散化搜索或随机采样生成一系列路径点，理论上能够保证路径的全局最优性或概率完备性{[}5−7{]}。然而，其生成的路径通常仅在几何意义上可行，往往缺乏对巡飞弹实际飞行运动学的约束，包括：最大速度限制、加速度限制；以及对动力学的约束，包括：转弯半径限制、俯仰角速率限制、滚转角速率限制。更重要的是，这类方法对环境的动态变化响应滞后，难以实时应对突发障碍或威胁；其生成的路径也通常需要额外的局部跟踪控制器进行跟随{[}8{]}，这可能导致跟踪误差累积或在动态环境下失效，造成规划与控制环节的脱节。由此可见，几何规划方法在动态环境适应性和运动学可行性方面存在显著不足。

另一方面，运动学/动力学跟踪控制方法，如模型预测控制（Model Predictive
Control,
MPC）直接基于巡飞弹的精确运动学或动力学模型生成控制指令，并通过滚动时域优化实时处理状态与输入约束以及避障要求{[}9−11{]}。MPC框架理论上能够紧密耦合规划与控制，提供满足约束的可行轨迹。然而，其核心挑战在于计算复杂度。对于高速机动的巡飞弹，需要在极短的控制周期内在线求解非线性优化问题，这对弹载计算资源提出了严峻挑战。特别是在高维状态空间、长预测时域或密集障碍物环境下，优化问题可能无法实时求解，导致控制延迟甚至失效，难以满足巡飞弹对高实时性的苛刻要求。因此，MPC方法虽然耦合了规划与控制，但其计算负担限制了其在复杂动态场景中的实时应用潜力。

上述两类方法的分离式设计，即"先全局规划后局部跟踪"或"仅局部优化"，在应对巡飞弹所面临的复杂动态环境，如移动障碍、突发威胁、多重硬约束时，难以同时兼顾全局路径效率、局部运动学可行性、严格安全保证与计算实时性。因此，为巡飞弹飞行设计能够融合全局优化能力与局部实时、安全响应能力的规划框架变得至关重要。
动态窗口法（Dynamic Window Approach, DWA）
作为一种经典的局部实时规划器，其核心思想是在满足运动学约束的速度空间内，直接采样并评估短时间内可行的轨迹，选择最优（如朝向目标最快、距离障碍物最远）的一条执行{[}12{]}。
DWA能够严格保证生成的速度指令在下一个控制周期内是运动学可行的且能即时避障，具有计算高效、响应快速的优势，非常适合作为复杂环境下的实时安全动作生成器。然而，DWA本质上是一种贪婪的局部优化方法，容易陷入局部最优，缺乏对路径长度、时间等全局任务目标和长期交互的考虑{[}13{]}。为此，需要结合能够进行全局策略优化的方法以弥补DWA的局限性。

强化学习（Reinforcement Learning, RL），特别是深度强化学习（Deep
Reinforcement Learning,
DRL）框架，为解决全局策略优化问题提供了新的思路。RL通过智能体与环境的交互学习最优策略，能够在马尔可夫决策过程（Markov
Decision Process,
MDP）框架下实现端到端的路径规划与控制优化，理论上能够克服DWA的局部最优性局限，学习到考虑长期累积奖励的全局最优或近似最优策略{[}14{]}。例如，文献{[}15{]}采用深度确定性策略梯度（DDPG）算法，初步实现了巡飞弹在动态障碍环境中的实时路径生成与避障控制；文献{[}16{]}提出基于近端策略优化（PPO）的三维航迹规划方法，有效处理了无人机在复杂城市环境中的避障问题；文献{[}17{]}利用软演员-评论家（SAC）算法优化了高速无人机的动态避碰策略；文献{[}18{]}则探索了多智能体强化学习（MARL）在无人机集群协同路径规划中的应用。这些研究展现了RL在复杂决策与环境适应性方面的潜力。然而，传统无约束RL方法在应用于安全关键系统（如巡飞弹）时存在显著缺陷：其一，难以显式保证生成的动作严格满足运动学约束与环境安全约束（如避障），可能导致危险动作的产生{[}19{]}；其二，其训练过程依赖于大量样本数据，在高维动态环境中的收敛速度较慢，且泛化能力有限{[}20{]}。这些安全性问题严重制约了RL在巡飞弹自主规划中的直接应用。

针对上述问题，近年来提出的约束策略优化（Constrained Policy Optimization,
CPO）框架有效弥补了强化学习在安全性方面的不足{[}21-22{]}。CPO通过在策略优化过程中显式引入约束条件，并提供严格的理论保证，使得生成的动作能够满足约束要求{[}23{]}。然而，CPO方法在复杂场景中仍存在局限性，例如对局部极小值的敏感性以及对环境模型精度的依赖。此外，单纯基于约束优化的强化学习方法在动态环境下的实时性仍难以满足任务需求。如何进一步结合经典运动规划方法的实时优势与强化学习的全局优化能力，成为运动规划领域的重要研究方向。这提示我们，将具有实时安全保证能力的DWA与具有全局约束优化能力的CPO进行有效融合，可能是一条有前景的解决路径。

综上所述，针对巡飞弹在复杂动态环境中对运动规划算法在全局优化性、实时安全性、运动学可行性及严格约束满足等方面提出的综合挑战，亟需突破传统分离式设计或单一优化范式的局限。本研究旨在CPO的全局策略学习与显式安全约束优化基础上，融合并改进DWA的实时避障与运动学约束保障能力，从而构建一种新型的协同规划框架。
基于此融合思路，本文的主要创新点如下：

1）针对巡飞弹在复杂飞行场景的运动学约束与动态环境，提出多目标加权的自适应评价函数，优化离散动作选取，实现高效高精度的自主控制；

2）构建改进DWA与CPO协同的层级规划框架：改进DWA层基于六自由度运动学模型直接生成满足实时避障与运动学约束的安全控制指令集；CPO层在此安全动作空间内优化长期累积奖励，突破局部最优限制；

3）设计分阶段课程学习机制，训练环境从简单静态场景逐步过渡至密集动态场景，加速算法收敛并增强对未知动态环境的适应性。

本文的研究不仅为复杂动态环境下巡飞弹的可靠自主规划提供了一种高效、鲁棒的解决方案，也为无人作战装备的智能化发展提供了新的理论与技术支持。

\subsection{\texorpdfstring{\textbf{1
运动学模型与任务场景描述}}{1 运动学模型与任务场景描述}}\label{ux8fd0ux52a8ux5b66ux6a21ux578bux4e0eux4efbux52a1ux573aux666fux63cfux8ff0}

\textbf{1.1 巡飞弹质点运动学模型}

巡飞弹作为一种新型智能化弹药，具备长时滞空侦察、自主目标识别与精确打击能力，在现代战场中扮演着日益重要的角色。其典型作战任务包括区域监视、高价值目标搜索与伺机打击。执行此类任务时，巡飞弹常在数百米至数千米的中低空域以亚音速飞行，具备较高的机动性以适应复杂环境。面对城市密集建筑群、飞行器或防空火力等动态威胁以及复杂地形，其运动规划需满足严格的实时性、安全性与高效性要求。

巡飞弹的飞行控制核心在于管理其三维空间位置、速度矢量方向与大小。虽然其实际动力学涉及滚转、俯仰、偏航等多自由度耦合，但对于大范围航路点间的运动规划问题，重点关注其质心的宏观运动轨迹、速度变化趋势及航向调整能力更为关键。
这种规划层级通常忽略滚转角及其变化率对质心运动的次要影响，将巡飞弹简化为一个受运动学约束，如速度范围、加速度限制、转弯半径限制的质点进行建模。此简化模型能有效捕捉其航迹特征，如航迹偏航角、航迹倾斜角及速度变化，同时显著降低规划问题的复杂度，是进行高效、可扩展运动规划算法的常用方法。

考虑巡飞弹的飞行特性，本文采用质点模型，忽略滚转角影响。巡飞弹状态向量定义为

\begin{equation}
\mathbf{x} = [x, y, z, v, \gamma, \psi]^T
\end{equation}

其中，$(x, y, z)$表示巡飞弹在地面坐标系中的位置坐标，$v$表示巡飞弹速度大小，$\gamma$表示航迹倾斜角，$\psi$表示航迹偏航角。

图1展示了巡飞弹质点运动学模型的三维坐标系示意图。该图清晰地描述了巡飞弹在地面坐标系中的位置、速度向量以及关键角度参数的几何关系。

% 注：图1需要替换为实际的图片文件
% \includegraphics[width=3.22847in,height=2.22569in]{media/image6.png}
\begin{center}
\textit{图1 巡飞弹质点运动学模型示意图（图片待插入）}
\end{center}

巡飞弹运动学方程为

\begin{equation}
\begin{aligned}
\dot{x} &= v\cos\gamma\cos\psi \\
\dot{y} &= v\cos\gamma\sin\psi \\
\dot{z} &= v\sin\gamma \\
\dot{v} &= a_x \\
\dot{\gamma} &= \frac{a_n\cos\beta - g\cos\gamma}{v} \\
\dot{\psi} &= \frac{a_n\sin\beta}{v\cos\gamma}
\end{aligned}
\end{equation}

其中，$a_x$表示轴向加速度，$a_n$表示法向加速度，$\beta$表示法向加速度的倾斜角，$g = 9.81$m/s²表示重力加速度。控制输入向量为

\begin{equation}
\mathbf{u} = [a_x, a_n, \beta]^T
\end{equation}

巡飞弹的运动约束集合$\mathcal{C}$基于实际飞行性能参数，速度约束表示为

\begin{equation}
v_{min} \leq v \leq v_{max}
\end{equation}

其中，$v_{min}$为失速速度，$v_{max}$为最大飞行速度。

加速度约束表示为

\begin{equation}
|a_x| \leq a_{x,max}, \quad |a_n| \leq a_{n,max}
\end{equation}

角度与角速度约束表示为

\begin{equation}
|\gamma| \leq \gamma_{max}, \quad |\dot{\gamma}| \leq \dot{\gamma}_{max}, \quad |\dot{\psi}| \leq \dot{\psi}_{max}
\end{equation}

安全约束表示为

\begin{equation}
d_{obs}(x,y,z) \geq d_{safe}, \quad (x,y,z) \in \mathcal{W}_{free}
\end{equation}

其中，$d_{obs}$为到最近障碍物的距离，$\mathcal{W}_{free}$为允许飞行区域。

\textbf{1.2 场景建模与问题描述}

巡飞弹在$\mathcal{W} \subset \mathbb{R}^3$这个三维空间中执行任务，环境包含静态障碍物集合$\mathcal{O}_s$和动态障碍物集合$\mathcal{O}_d(t)$。环境中的静态障碍物为固定位置的立方体或球体，表示建筑物、地形等；动态障碍物为具有预定轨迹的移动物体，表示其他飞行器或移动威胁。

巡飞弹需要从起始位置$\mathbf{x}_0$安全到达目标位置$\mathbf{x}_g$，同时满足运动约束（速度约束、加速度约束、角度约束）、安全约束（避障约束、飞行区域约束）以及任务要求：尽可能提高路径效率和路径质量。巡飞弹的多约束规划问题可以表述为

\begin{equation}
\begin{aligned}
\min_{\pi} \quad & J(\pi) = \mathbb{E}_{\pi}\left[\sum_{t=0}^{T} c(x_t, u_t)\right] \\
\text{s.t.} \quad & x_{t+1} = f(x_t, u_t) \\
& u_t \in \mathcal{U}_{safe}(x_t) \\
& x_t \in \mathcal{X}_{valid}
\end{aligned}
\end{equation}

其中$J(\pi)$为累积代价函数，$f(\cdot)$为状态转移函数，$\mathcal{U}_{safe}$为安全控制输入集合，$\mathcal{X}_{valid}$为有效状态空间。

\subsection{\texorpdfstring{\textbf{2
DWA-RL融合架构设计}}{2 DWA-RL融合架构设计}}\label{dwa-rlux878dux5408ux67b6ux6784ux8bbeux8ba1}

本文实现的DWA-RL融合架构将局部动态规划与全局优化相结合，解决了传统方法的局限性。该架构的核心思想是：DWA作为局部动态规划器提供即时安全保证，TD3作为全局优化器通过学习实现长期最优决策。这种设计突破了传统DWA方法的局部最优和短视性问题，同时保持了强化学习的环境适应性和全局优化能力。

% 注：图2需要替换为实际的架构图
\begin{center}
\textit{图2 DWA-RL融合架构示意图（图片待插入）}
\end{center}

改进DWA层负责局部安全约束，在每个决策时刻生成满足所有运动约束的安全控制输入集合；TD3层负责全局策略优化，通过学习动态选择最优动作，实现超越局部贪心的全局最优路径规划。

策略学习层通过与局部约束层生成的安全控制输入交互，学习最优策略$\pi^*(s)$，其中动作$a$通过映射函数转换为控制输入$u$，优化长期累积奖励：

\begin{equation}
J(\pi) = \mathbb{E}_{\pi}\left[\sum_{t=0}^{\infty} \gamma^t r(s_t, a_t)\right]
\end{equation}

这种分离设计确保了安全性的绝对保证与性能的持续优化的有机统一，实现了约束满足与策略学习的解耦。

\subsection{\texorpdfstring{\textbf{3
基于CPO框架的巡飞弹运动规划算法}}{3 基于CPO框架的巡飞弹运动规划算法}}\label{ux57faux4e8ecpoux6846ux67b6ux7684ux5de1ux98deux5f39ux8fd0ux52a8ux89c4ux5212ux7b97ux6cd5}

\textbf{3.1 安全约束层---动态窗口法实现}

作为硬约束保证机制，改进的DWA层基于当前状态和动态窗口直接生成满足所有运动约束$\mathcal{C}$的安全控制输入集合。基于巡飞弹运动学模型的控制输入动态窗口计算为

\begin{equation}
\mathcal{U}_{DW}(x) = \{u = [a_x, a_n, \beta]^T : u \in \mathcal{U}_{kinematic} \cap \mathcal{U}_{collision}
\end{equation}

碰撞避免约束基于轨迹预测：

\begin{equation}
\mathcal{U}_{collision} = \{u : \tau(x, u, T) \cap (\mathcal{O}_s \cup \mathcal{O}_d(t)) = \emptyset\}
\end{equation}

轨迹预测函数$\tau(x, u, T)$基于巡飞弹的六自由度运动学模型，在设定的预测时间窗口内计算控制输入$u$产生的轨迹：

\begin{equation}
\tau(x, u, T) = \{x(t) : t \in [0, T], \dot{x}(t) = f(x(t), u)\}
\end{equation}

其中，运动状态按控制输入演进：

\begin{equation}
x(t+\Delta t) = x(t) + f(x(t), u) \cdot \Delta t
\end{equation}

预测时间窗口的设置确保DWA能够预见机动轨迹并提前规避碰撞风险，并且避免传统方法中从速度空间到控制输入的转换步骤，为巡飞弹提供了更直接和精确的运动控制能力。

\textbf{DWA多目标评价函数设计}

DWA在筛选安全控制输入时采用多目标评价函数，综合考虑导航性能、速度效率、距离优化和安全性：

\begin{equation}
J_{DWA}(u) = \alpha \cdot J_{heading} + \beta \cdot J_{speed} + \gamma \cdot J_{distance} + \delta \cdot J_{safety}
\end{equation}

其中各分量定义如下：

\textbf{1）航向评价$J_{heading}$}：通过预测轨迹方向与目标方向的点积计算
\begin{equation}
J_{heading} = \max(0, \mathbf{d}_{movement} \cdot \mathbf{d}_{goal})
\end{equation}
其中$\mathbf{d}_{movement}$为预测运动方向，$\mathbf{d}_{goal}$为目标方向单位向量。

\textbf{2）速度评价$J_{speed}$}：基于距离自适应的最优速度策略
\begin{equation}
J_{speed} = 1 - \frac{|V_{final} - V_{optimal}(d_{goal})|}{V_{max} - V_{min}}
\end{equation}
其中$V_{optimal}(d_{goal})$根据到目标距离动态调整：远距离时鼓励高速巡航（45m/s），近距离时要求减速精确接近（18m/s）。

\textbf{3）距离评价$J_{distance}$}：鼓励接近目标的非线性函数
\begin{equation}
J_{distance} = \frac{1}{1 + d_{goal}/100}
\end{equation}

\textbf{4）安全评价$J_{safety}$}：基于最小障碍物距离的安全裕度
\begin{equation}
J_{safety} = \min\left(\frac{d_{obs,min}}{50}, 1.0\right)
\end{equation}
其中$d_{obs,min}$为到最近障碍物的距离。

权重设置为：$\alpha = 0.4$（航向权重），$\beta = 0.2$（速度权重），$\gamma = 0.3$（距离权重），$\delta = 0.1$（安全权重）。

另外，为保证控制精度的同时实现高效的安全动作生成，需合理设置$\mathcal{U}_{DW}$的离散化分辨率。分辨率选择直接决定DWA的计算复杂度和轨迹控制精度。离散化采样规模由分辨率$\Delta$决定。计算复杂度呈立方增长:

\begin{equation}
O(|\mathcal{U}_{DW}|) = O(N_{a_T} \times N_{a_N} \times N_{\mu})
\end{equation}

本文依据巡飞弹运动特性和计算效率要求，采用优化的分辨率设置：$a_T$分辨率4.0 m/s²，$a_N$分辨率15.0 m/s²，$\mu$分辨率0.5 rad，确保单步计算时间控制在10ms以内。

\textbf{3.2 策略学习层---基于约束策略优化的TD3网络}

强化学习CPO理论框架在DWA方法筛选产生的安全控制输入集$\mathcal{U}_{safe}$内进行策略学习。采用TD3（Twin
Delayed Deep Deterministic Policy
Gradient）算法实现约束动作空间内的连续控制策略优化，通过深度神经网络逼近最优策略函数，同时保持与安全约束层的协同工作。基于前端安全控制输入的特点，设计约束感知的Actor和Critic网络。

Actor网络$\pi_\theta(s)$采用深度前馈神经网络结构，实现状态到动作的非线性映射。该网络通过三层全连接结构实现特征提取和动作生成，其前向传播过程表示为

\begin{equation}
\pi_\theta(s) = \tanh(W_3 \cdot \text{ReLU}(W_2 \cdot \text{ReLU}(W_1 s + b_1) + b_2) + b_3)
\end{equation}

其中，$\theta$为网络参数，$s$为动作空间归一化因子，$\tanh$激活函数确保输出动作在有界空间$[-1,1]$内。

\textbf{3.2 巡飞弹飞行的马尔科夫决策建模}

巡飞弹在复杂三维空间中的自主导航本质上是一个连续状态-连续动作的序贯决策问题。为确保该问题满足马尔科夫性质，系统状态必须包含做出最优决策所需的全部历史信息。

\textbf{马尔科夫状态的理论要求}：理论上，完整的马尔科夫状态应包含巡飞弹运动状态、环境障碍物信息、目标位置和任务进展等所有决策相关信息。然而，在实际实现中，需要将这些抽象信息转换为具体的可观测特征。

\textbf{观测空间的工程实现}：基于马尔科夫性质要求，本文设计了融合运动学状态、环境感知和任务目标的观测向量：

\begin{equation}
\mathbf{o}_t = \phi(s_t) \in \mathbb{R}^{15}
\end{equation}

其中$\phi(\cdot)$为状态观测函数，将完整的马尔科夫状态$s_t$映射为15维观测向量$\mathbf{o}_t$。该观测向量的具体组成为：

\begin{equation}
\mathbf{o}_t = \begin{bmatrix}
x_t/x_{\max}, y_t/y_{\max}, z_t/z_{\max} \\
V_t/V_{\max}, \gamma_t/\gamma_{\max}, \psi_t/(2\pi) \\
\hat{\mathbf{d}}_{\text{goal}} = [\hat{g}_x, \hat{g}_y, \hat{g}_z]^T \\
d_{\text{goal}}/100.0, d_{\text{obs}}/50.0 \\
N_{\text{static}}/20.0, N_{\text{dynamic}}/10.0 \\
\text{flag}_{\text{dynamic}}, z_t/z_{\max}
\end{bmatrix} \in \mathbb{R}^{15}
\end{equation}

其中各分量的物理意义为：

\begin{itemize}
\item \textbf{运动学状态}（1-6维）：归一化的位置$[x_t, y_t, z_t]$、速度$V_t$、航迹倾斜角$\gamma_t$和偏航角$\psi_t$
\item \textbf{任务导向}（7-10维）：单位目标方向向量$\hat{\mathbf{d}}_{\text{goal}}$和归一化目标距离$d_{\text{goal}}$
\item \textbf{环境感知}（11-15维）：归一化最近障碍物距离$d_{\text{obs}}$、静态/动态障碍物数量$N_{\text{static}}, N_{\text{dynamic}}$、动态环境标志$\text{flag}_{\text{dynamic}} \in \{0,1\}$，以及高度信息的冗余编码
\end{itemize}

值得注意的是，当前实现中第15维与第3维存在信息冗余。这一冗余可在未来工作中进一步优化，例如用相对高度$\Delta z = z_t - z_{\text{goal}}$或飞行时间比例$t/t_{\max}$等更有价值的特征替代，以提升观测向量的信息密度。

这种设计确保观测向量$\mathbf{o}_t$包含了策略学习所需的充分信息，满足马尔科夫决策过程的理论要求。

\textbf{动作空间约束}：不同于传统RL的自由动作空间，本系统的可执行动作空间$\mathcal{A}_t \subset \mathbb{R}^3$由DWA实时生成，具有状态依赖性：
\begin{equation}
\mathcal{A}_t = \text{DWA}(\mathbf{o}_t) = \{a_i \in [-1,1]^3 : \text{Safe}(\mathbf{o}_t, a_i) = \text{True}\}
\end{equation}

这种设计确保TD3的每个决策都在运动学和安全约束范围内，避免了传统RL中的约束违反问题。

\textbf{奖励函数的协同设计}：奖励函数$r(\mathbf{o}_t,a_t)$专门设计用于优化DWA无法处理的长期指标：
\begin{equation}
r(\mathbf{o}_t,a_t) = r_{\text{energy}}(a_t) + r_{\text{smooth}}(a_t,a_{t-1}) + r_{\text{strategy}}(\mathbf{o}_t,a_t) + r_{\text{progress}}(\mathbf{o}_t)
\end{equation}

该建模框架通过观测函数$\phi(\cdot)$建立了理论马尔科夫状态与工程实现的桥梁，既保证了理论的严谨性，又实现了系统的可操作性。

\textbf{面向巡飞弹任务的TD3网络架构设计}

基于上述马尔科夫建模，本文设计了专门适配该任务特性的TD3网络架构。该架构需要处理高维连续观测、约束动作选择和长期策略优化三个核心挑战。

\textbf{Actor网络的任务适配设计}：
Actor网络$\pi_\theta: \mathbb{R}^{15} \rightarrow \mathbb{R}^3$需要从15维观测空间映射到3维归一化动作空间，其架构考虑了巡飞弹观测的层次化特性。

网络设计要点：1）\textbf{输入层处理}：15维观测经过标准化处理，其中运动学状态采用MinMax归一化，环境感知信息采用对数尺度变换，确保不同物理量级的特征均衡学习。2）\textbf{隐藏层设计}：采用[256,256]的全连接架构，兼顾了表达能力和计算效率，能够有效学习观测-动作映射的非线性关系。3）\textbf{输出约束}：$\tanh$激活函数将输出限制在$[-1,1]^3$，对应归一化的[切向加速度，法向加速度，倾斜角速率]控制输入。

\textbf{双Critic网络的价值评估设计}：
考虑到巡飞弹任务中价值函数的复杂性，系统采用双Critic架构$Q_{\phi_1}, Q_{\phi_2}: \mathbb{R}^{15} \times \mathbb{R}^3 \rightarrow \mathbb{R}$：

\begin{equation}
Q_{\phi_i}(\mathbf{o},a) = W_3^{(i)} \cdot \text{ReLU}(W_2^{(i)} \cdot \text{ReLU}(W_1^{(i)}[\mathbf{o};a] + b_1^{(i)}) + b_2^{(i)}) + b_3^{(i)}
\end{equation}

双网络设计的任务适配性：1）\textbf{输入融合}：观测-动作串联$[\mathbf{o};a] \in \mathbb{R}^{18}$通过早期融合机制，使网络能够学习观测与动作间的复杂交互关系。2）\textbf{过估计抑制}：通过$\min(Q_{\phi_1}, Q_{\phi_2})$操作，有效应对巡飞弹长时域任务中的价值函数过估计问题。3）\textbf{稳定性保障}：双网络独立训练但目标一致，在复杂动态环境中提供更加鲁棒的价值估计。

\textbf{针对约束动作空间的优化机制}：
不同于标准TD3，本系统的优化过程考虑了DWA生成的动态约束：

\begin{equation}
a^* = \arg\max_{a \in \mathcal{A}_t} \min(Q_{\phi_1}(\mathbf{o}_t,a), Q_{\phi_2}(\mathbf{o}_t,a))
\end{equation}

这种约束优化确保了策略学习始终在安全可行域内进行，避免了传统RL中策略违反物理约束的问题。同时，通过在变化的约束集合$\mathcal{A}_t$内进行优化，网络学会了适应不同飞行状态下的约束条件，提高了策略的泛化能力。该网络架构设计充分考虑了巡飞弹飞行任务的特殊性，通过任务导向的网络结构、约束感知的优化机制和协同分工的设计理念，实现了高效稳定的策略学习。

\textbf{3.3 策略优化---奖励函数设计}

基于强化学习理论和巡飞弹任务特性，本文设计了以巡飞弹完成任务的全局路径效率为核心的奖励函数。该函数专注于优化DWA无法处理的长期指标，实现了明确的职责分工。

本文奖励函数设计遵循"协同分工"原则：DWA负责短期安全约束和局部最优决策，TD3专注于长期全局优化策略。因此，奖励函数不再包含DWA已经优化的短期安全和导航指标，而是专门设计来优化DWA短视性无法处理的长期性能指标。

该奖励函数每个组件都有明确的理论依据和实际意义：

\textbf{1）能耗效率评估}：

$r_{energy}$基于控制输入的归一化能耗效率计算：

\begin{equation}
r_{energy} = \max\left(0, 1 - \frac{||\mathbf{u}_t||^2}{||\mathbf{u}_{max}||^2}\right) \times 15.0
\end{equation}

其中，$\mathbf{u}_t$为当前控制输入，$\mathbf{u}_{max}$为最大控制输入。该组件通过正向奖励鼓励TD3选择能耗较低的控制策略，优化长期燃料经济性。

\textbf{2）路径平滑性评估：}

$r_{smooth}$基于控制输入变化率的平滑性计算：

\begin{equation}
r_{smooth} = \max\left(0, 1 - \frac{||\mathbf{u}_t - \mathbf{u}_{t-1}||}{||\mathbf{u}_{max}|| \times 2}\right) \times 12.0
\end{equation}

该组件通过平滑性评分鼓励TD3产生平滑的控制序列，避免急剧的机动变化，提高飞行品质和结构安全性。

\textbf{3）全局策略优化评估：}

$r_{strategy}$综合评估TD3从DWA安全动作集中的选择质量：

\begin{equation}
r_{strategy} = \left(0.6 \times \eta_{path} + 0.4 \times \eta_{time}\right) \times 12.0
\end{equation}

其中，路径效率$\eta_{path}$和时间效率$\eta_{time}$的详细计算如下：

\textbf{路径效率计算：}
\begin{equation}
\eta_{path} = \min\left(1.0, \frac{d_{ideal}}{d_{traveled} + \epsilon}\right)
\end{equation}
其中，$d_{ideal} = \|\mathbf{x}_g - \mathbf{x}_0\|$为起点到终点的理想直线距离，$d_{traveled} = \sum_{t=1}^{T} \|\mathbf{x}_t - \mathbf{x}_{t-1}\|$为实际飞行轨迹的累积距离。

\textbf{时间效率计算：}
\begin{equation}
\eta_{time} = \min\left(1.0, \frac{P_{current}}{t_{ratio} + \epsilon}\right)
\end{equation}
其中，$P_{current} = 1 - \frac{d_{current}}{d_{ideal}}$为当前任务完成进度，$t_{ratio} = \frac{t_{current}}{t_{max}}$为当前时间占最大允许时间的比例。

该组件通过综合路径和时间两个维度评估TD3选择策略的全局优化质量，鼓励智能体在保证路径最短的同时快速完成任务。

此外，协同进度奖励$r_{progress}$根据当前效率状态自适应调整：

\begin{equation}
r_{progress} = \begin{cases}
(d_{prev} - d_{curr}) \times 2.0 & \text{if } \eta_{energy} > 0.6 \text{ and } \eta_{smooth} > 0.5 \\
(d_{prev} - d_{curr}) \times 0.5 & \text{otherwise}
\end{cases}
\end{equation}

总奖励函数定义为：

\begin{equation}
r_{total} = r_{energy} + r_{smooth} + r_{strategy} + r_{progress} - 0.3
\end{equation}

成功到达目标时，基于整个episode的三项指标平均值给予终端额外奖励：

\begin{equation}
r_{terminal} = k_{bonus} \cdot (\bar{r}_{energy} + \bar{r}_{smooth} + \bar{r}_{global})
\end{equation}

该奖励函数设计通过明确的职责分工避免了与DWA功能的重复和冲突；专注于长期优化指标，弥补了DWA短视性的不足；三项核心指标直接对应实际工程需求，如燃料效率、飞行品质、任务效果。

\subsection{\texorpdfstring{\textbf{4
仿真训练与结果分析}}{4 仿真训练与结果分析}}\label{ux4effux771fux8badux7ec3ux4e0eux7ed3ux679cux5206ux6790}

\textbf{4.1 DWA-TD3协同训练机制}

本文实现了基于约束优化的DWA-TD3协同训练机制，该机制将动态窗口法的实时安全保证与TD3网络的全局策略优化相结合，实现了安全约束下的最优策略学习。算法1描述了完整的训练流程。

\begin{algorithm}[h]
\caption{DWA-TD3约束优化训练算法}
\label{alg:dwa_td3_constrained}
\begin{algorithmic}[1]
\REQUIRE 初始状态$s_0$，目标位置$\mathbf{g}_{goal}$，环境$\mathcal{E}$
\ENSURE 训练后的策略网络$\pi_\theta$，价值网络$Q_{\phi_1}, Q_{\phi_2}$
\STATE 初始化Actor网络$\pi_\theta$，双Critic网络$Q_{\phi_1}, Q_{\phi_2}$
\STATE 初始化目标网络$\pi_{\bar{\theta}}, Q_{\bar{\phi}_1}, Q_{\bar{\phi}_2}$
\STATE 初始化经验回放缓冲区$\mathcal{D} = \emptyset$
\FOR{episode $= 1$ to $N_{episodes}$}
    \STATE 重置环境获取初始状态：$s_0 = \text{Environment.reset()}$
    \STATE $t = 0, \mathbf{o}_t = \phi(s_t)$
    \WHILE{not terminal and $t < T_{max}$}
        \STATE $\mathcal{U}_{safe} = \text{DWA}(\mathbf{o}_t, \mathcal{E}, \mathbf{g}_{goal})$ \COMMENT{DWA生成安全控制集}
        \IF{$|\mathcal{U}_{safe}| > 0$}
            \STATE $\mathcal{A}_{safe} = \{\phi_{norm}(u_i) : u_i \in \mathcal{U}_{safe}\}$ \COMMENT{转换为归一化动作}
            \STATE $Q_{best} = -\infty, a_t^* = \mathcal{A}_{safe}[0]$
            \FOR{$a_i \in \mathcal{A}_{safe}$} \COMMENT{TD3从安全集合中选择最优动作}
                \STATE $Q_i = \min(Q_{\phi_1}(\mathbf{o}_t, a_i), Q_{\phi_2}(\mathbf{o}_t, a_i))$
                \IF{$Q_i > Q_{best}$}
                    \STATE $Q_{best} = Q_i, a_t^* = a_i$
                \ENDIF
            \ENDFOR
        \ELSE
            \STATE $a_t^* = [-0.5, 0.0, 0.0]$ \COMMENT{紧急制动}
        \ENDIF
        \STATE $u_t = \phi_{denorm}(a_t^*)$ \COMMENT{转换为实际控制输入}
        \STATE $\mathbf{o}_{t+1}, r_t, done = \text{Environment.step}(u_t)$
        \STATE $\mathcal{D} = \mathcal{D} \cup \{(\mathbf{o}_t, a_t^*, r_t, \mathbf{o}_{t+1}, done)\}$
        \IF{$|\mathcal{D}| \geq \text{batch\_size}$}
            \STATE 采样批次数据，更新Critic网络$Q_{\phi_1}, Q_{\phi_2}$
            \IF{$t \bmod \text{policy\_freq} = 0$}
                \STATE 延迟更新Actor网络$\pi_\theta$，软更新目标网络
            \ENDIF
        \ENDIF
        \STATE $t = t + 1$
    \ENDWHILE
\ENDFOR
\RETURN $\pi_\theta, Q_{\phi_1}, Q_{\phi_2}$
\end{algorithmic}
\end{algorithm}

算法1总结了DWA-TD3融合架构的核心步骤。该算法的关键创新在于实现了约束满足与策略优化的完全解耦：DWA层在第10行基于当前观测和环境信息生成严格满足运动学约束和避障要求的安全控制集合$\mathcal{U}_{safe}$，确保后续决策的绝对安全性；第12行将物理控制输入转换为归一化动作空间$\mathcal{A}_{safe}$，建立与TD3网络的接口；第14-19行展现了约束优化的核心机制，TD3网络通过双Critic架构评估每个安全动作的长期价值，选择Q值最高的动作执行，实现了在严格约束下的全局最优决策。

与传统强化学习方法不同，该算法避免了探索过程中的安全违规问题。第21行的紧急制动机制为极端情况提供了安全保障，而第25-29行的网络更新过程则确保了策略的持续优化。算法通过经验回放机制存储有效的状态-动作对，并采用TD3特有的延迟更新策略降低过估计偏差，保证了训练过程的稳定性和收敛性。该设计实现了理论严谨性与工程可靠性的统一，为巡飞弹在复杂动态环境中的安全自主导航提供了有效的技术路径。

\textbf{4.2 仿真环境与参数设置}

在仿真时，为了避免随机初始化导致的学习效率低下问题，系统能够自动计算指向目标的初始航迹角和俯仰角：

\begin{equation}
\begin{aligned}
\psi_0 &= \arctan2(y_g - y_0, x_g - x_0) \\
\gamma_0 &= \arctan2(z_g - z_0, \sqrt{(x_g-x_0)^2 + (y_g-y_0)^2})
\end{aligned}
\end{equation}

其中，$\mathbf{d} = [x_g-x_0, y_g-y_0, z_g-z_0]^T$为起点到终点的向量，$\|\mathbf{d}\|$为距离。

为模拟巡飞弹真实任务飞行环境，设置仿真在2000×2000×2000m的三维空间中进行。实验环境基于Python 3.8与PyTorch 1.12.0深度学习框架构建，采用NVIDIA GeForce RTX 3080显卡加速训练过程。

\textbf{巡飞弹运动学参数设置}

基于某型巡飞弹的实际飞行性能指标，本文设置的运动学约束参数如表1所示。

\begin{table}[htbp]
\centering
\caption{巡飞弹运动学约束参数}
\begin{tabular}{|c|c|c|}
\hline
\textbf{参数} & \textbf{符号} & \textbf{数值} \\
\hline
最小飞行速度 & $v_{min}$ & 15 m/s \\
\hline
最大飞行速度 & $v_{max}$ & 60 m/s \\
\hline
巡航速度 & $v_{cruise}$ & 25 m/s \\
\hline
最大切向加速度 & $a_{T,max}$ & $\pm$8 m/s² \\
\hline
最大法向加速度 & $a_{N,max}$ & $\pm$39.24 m/s² (4g) \\
\hline
最大航迹倾斜角 & $\gamma_{max}$ & $\pm$60° \\
\hline
安全距离阈值 & $d_{safe}$ & 5 m \\
\hline
控制时间步长 & $\Delta t$ & 0.1 s \\
\hline
\end{tabular}
\end{table}

\textbf{仿真环境配置参数}

实验设计了三种复杂度递增的测试环境以验证算法的环境适应性，具体配置如表2所示。

\begin{table}[htbp]
\centering
\caption{仿真环境配置参数}
\begin{tabular}{|c|c|c|c|}
\hline
\textbf{环境类型} & \textbf{静态障碍物数量} & \textbf{动态障碍物数量} & \textbf{障碍物速度范围} \\
\hline
阶段1：简单静态环境 & 6-8 & 0 & - \\
\hline
阶段2：复杂静态环境 & 12-15 & 0 & - \\
\hline
阶段3：动态环境 & 8-10 & 4-6 & $\pm$10 m/s \\
\hline
\end{tabular}
\end{table}

\textbf{障碍物设计与建模}

\textbf{1）静态障碍物设计}

静态障碍物采用球体几何形状，模拟城市建筑、地形特征和防空威胁区域。基于路径阻挡的挑战性布局策略，障碍物主要分布在起点到终点的连线附近，确保算法必须进行有效的避障机动。

静态障碍物分为两类：\textbf{主要障碍物}和\textbf{辅助障碍物}。主要障碍物沿巡飞弹预期航路分布，在路径的20\%、35\%、50\%、65\%、80\%、95\%位置附近设置，形成连续的避障挑战。辅助障碍物在路径两侧200-400m范围内随机分布，增加环境复杂度。

\begin{table}[htbp]
\centering
\caption{静态障碍物参数设置}
\begin{tabular}{|c|c|c|c|}
\hline
\textbf{障碍物类型} & \textbf{半径范围(m)} & \textbf{数量占比} & \textbf{分布策略} \\
\hline
主要障碍物 & 150-200 & 约2/3 & 路径连线附近，$\pm$100m偏移 \\
\hline
辅助障碍物 & 120-180 & 约1/3 & 路径两侧，200-400m距离 \\
\hline
\end{tabular}
\end{table}

\textbf{2）动态障碍物设计}

动态障碍物模拟空域中的移动威胁，同样采用球体几何形状，具有三种运动模式：

- \textbf{直线运动}：速度$v \sim U(-10, 10)$m/s (三维)，在边界内反弹，模拟直线巡逻飞行器
- \textbf{圆形轨道运动}：轨道半径$R \sim U(50, 150)$m，角速度$\omega \sim U(0.1, 0.5)$rad/s，模拟定点巡逻
- \textbf{振荡运动}：基于基准点振荡，振幅$A \sim U(20, 80)$m，频率$f \sim U(0.1, 0.3)$Hz，模拟机动目标

动态障碍物的碰撞检测采用球形包络模型，半径与静态障碍物相同：

\begin{equation}
\mathcal{O}_d^i(t) = \{(x,y,z) : \|(x,y,z) - \mathbf{c}_i(t)\| \leq R_i\}
\end{equation}

其中$\mathbf{c}_i(t)$为动态障碍物的实时位置，$R_i$为障碍物半径。

\begin{table}[htbp]
\centering
\caption{动态障碍物运动模式参数}
\begin{tabular}{|c|c|c|c|}
\hline
\textbf{运动模式} & \textbf{半径范围(m)} & \textbf{运动参数} & \textbf{模拟目标} \\
\hline
直线运动 & 120-180 & 速度: $\pm$10 m/s (3D) & 巡逻飞行器 \\
\hline
圆形轨道 & 120-180 & 轨道半径: 50-150m & 定点警戒 \\
\hline
振荡运动 & 120-180 & 振幅: 20-80m, 频率: 0.1-0.3Hz & 机动威胁 \\
\hline
\end{tabular}
\end{table}

\textbf{3）障碍物生成算法}

为确保测试场景的可重复性和挑战性，采用分阶段障碍物生成算法：

\textbf{第一阶段：可达性验证}
- 使用A*算法验证起点到终点存在可行路径
- 若无可行路径，调整障碍物位置或减少障碍物密度

\textbf{第二阶段：复杂度调整}
- 根据训练进度动态调整障碍物密度和运动复杂度
- 简单环境：障碍物稀疏分布，运动模式单一
- 复杂环境：障碍物密集分布，多种运动模式混合

\textbf{第三阶段：安全边界设置}
- 在起点和终点周围设置半径100m的无障碍区域
- 确保巡飞弹有足够的机动空间进行起飞和着陆

起始位置和目标位置采用固定设置：起点为$[200, 200, 200]$m，终点为$[1800, 1800, 1800]$m，直线距离约2770m。这种固定设置确保了训练的一致性和可重复性，同时障碍物位置的随机性保证了训练场景的多样性。

\textbf{4）仿真场景可视化与验证}

为便于算法性能分析和结果展示，构建了三维可视化仿真环境，支持实时轨迹显示和性能监控：

- \textbf{三维场景渲染}：使用OpenGL渲染引擎实时显示巡飞弹位置、障碍物分布、预测轨迹和安全包络
- \textbf{轨迹记录与回放}：记录完整的飞行轨迹、控制指令序列和环境交互数据，支持离线分析
- \textbf{性能指标实时监控}：显示当前速度、加速度、约束违反情况、累积奖励等关键指标
- \textbf{多视角观察}：支持自由视角、跟随视角、俯视图等多种观察模式

\textbf{5）测试场景分类}

根据实际作战需求，设计了五种典型测试场景：

\begin{table}[htbp]
\centering
\caption{典型测试场景设计}
\begin{tabular}{|c|c|c|}
\hline
\textbf{场景类型} & \textbf{环境特征} & \textbf{考核重点} \\
\hline
城市低空突防 & 密集建筑物，狭窄通道 & 精确避障，路径优化 \\
\hline
空域机动对抗 & 多个高速动态威胁 & 动态规避，实时响应 \\
\hline
复合威胁环境 & 静态+动态障碍混合 & 综合决策，多目标权衡 \\
\hline
极限机动测试 & 高密度障碍分布 & 算法鲁棒性，安全边界 \\
\hline
长距离巡航 & 稀疏障碍，大范围飞行 & 能耗优化，全局规划 \\
\hline
\end{tabular}
\end{table}

每种场景设计30个不同难度的子场景，共计150个标准测试用例，确保算法性能评估的全面性和可比性。

\textbf{DWA算法参数设置}

改进动态窗口法的关键参数配置如表3所示，这些参数的选择平衡了计算效率与控制精度的要求。

\begin{table}[htbp]
\centering
\caption{DWA算法参数设置}
\begin{tabular}{|c|c|c|}
\hline
\textbf{参数} & \textbf{符号} & \textbf{数值} \\
\hline
预测时间窗口 & $T_{pred}$ & 1.0 s \\
\hline
控制周期 & $\Delta t$ & 0.1 s \\
\hline
切向加速度分辨率 & $\Delta a_T$ & 4.0 m/s² \\
\hline
法向加速度分辨率 & $\Delta a_N$ & 15.0 m/s² \\
\hline
倾斜角分辨率 & $\Delta \mu$ & 0.5 rad \\
\hline
最小安全距离 & $d_{safe}$ & 5.0 m \\
\hline
目标方向权重 & $\alpha$ & 0.4 \\
\hline
速度权重 & $\beta$ & 0.2 \\
\hline
距离权重 & $\gamma$ & 0.3 \\
\hline
障碍物权重 & $\delta$ & 0.1 \\
\hline
\end{tabular}
\end{table}

\textbf{TD3神经网络参数设置}

TD3算法的网络架构和训练参数配置如表4所示，Actor和Critic网络均采用三层全连接结构。

\begin{table}[htbp]
\centering
\caption{TD3神经网络参数设置}
\begin{tabular}{|c|c|c|}
\hline
\textbf{参数} & \textbf{符号} & \textbf{数值} \\
\hline
Actor网络结构 & - & [状态维度, 256, 256, 动作维度] \\
\hline
Critic网络结构 & - & [状态+动作维度, 256, 256, 1] \\
\hline
Actor学习率 & $\alpha_\pi$ & 1e-4 \\
\hline
Critic学习率 & $\alpha_Q$ & 3e-4 \\
\hline
折扣因子 & $\gamma$ & 0.99 \\
\hline
软更新系数 & $\tau$ & 0.005 \\
\hline
延迟更新频率 & $d$ & 2 \\
\hline
目标策略噪声标准差 & $\sigma$ & 0.2 \\
\hline
噪声裁剪范围 & $c$ & 0.5 \\
\hline
经验回放缓冲区大小 & $N_{buffer}$ & 1e6 \\
\hline
批处理大小 & $N_{batch}$ & 256 \\
\hline
\end{tabular}
\end{table}

\textbf{训练与评估设置}

采用分阶段课程学习策略，训练过程分为三个阶段，每个阶段的具体设置如表5所示。

\begin{table}[htbp]
\centering
\caption{分阶段训练参数设置}
\begin{tabular}{|c|c|c|c|c|}
\hline
\textbf{训练阶段} & \textbf{环境复杂度} & \textbf{训练episode数} & \textbf{随机/固定划分} & \textbf{总episode数} \\
\hline
阶段1：简单静态 & 6-8个静态障碍物 & 200/150 & 随机探索/固定强化 & 350 \\
\hline
阶段2：复杂静态 & 12-15个静态障碍物 & 250/200 & 随机探索/固定强化 & 450 \\
\hline
阶段3：动态环境 & 8-10静态+4-6动态 & 200/150 & 随机探索/固定强化 & 350 \\
\hline
\end{tabular}
\end{table}

评估指标包括：任务成功率、平均路径长度、平均完成时间、平均能耗、约束违反次数等。每个测试场景运行500个episode，取平均值作为最终结果。为确保结果的可重复性，设置固定随机种子42，最大步数限制为2000步，目标到达半径为50m。

\textbf{4.3 实验结果与分析}

为验证所提出的DWA-TD3融合算法的有效性，本文设计了多组对比实验，将其与现有主流方法进行对比分析。

\textbf{基线方法对比}

选择以下四种代表性算法作为基线方法进行对比：

1）\textbf{传统DWA}：经典动态窗口法，采用贪婪策略选择局部最优动作；

2）\textbf{A*+DWA}：结合A*全局路径规划与DWA局部避障的混合方法；

3）\textbf{Pure TD3}：无约束的TD3强化学习算法，直接从原始动作空间学习；

4）\textbf{PPO-Constrained}：基于约束策略优化的PPO算法，采用拉格朗日乘数法处理约束。

\textbf{训练收敛性分析}

训练收敛曲线分析表明，本文提出的DWA-TD3算法在所有环境中都表现出良好的收敛性能。

\begin{table}[htbp]
\centering
\caption{算法收敛性能比较}
\begin{tabular}{|c|c|c|c|}
\hline
\textbf{算法} & \textbf{收敛episode数} & \textbf{最终成功率(\%)} & \textbf{训练稳定性} \\
\hline
传统DWA & - & 72.3 & 高 \\
\hline
A*+DWA & - & 78.5 & 中等 \\
\hline
Pure TD3 & 85000 & 68.9 & 低 \\
\hline
PPO-Constrained & 75000 & 74.2 & 中等 \\
\hline
DWA-TD3(本文) & 45000 & 89.7 & 高 \\
\hline
\end{tabular}
\end{table}

从表6可以看出，本文方法在收敛速度和最终性能方面均优于对比方法，收敛episode数减少约47\%，最终成功率提升约14\%。

\textbf{性能指标对比分析}

在不同复杂度环境下的详细性能对比如表7所示。

\begin{table}[htbp]
\centering
\caption{不同环境下算法性能对比}
\begin{tabular}{|c|c|c|c|c|c|}
\hline
\textbf{环境类型} & \textbf{算法} & \textbf{成功率(\%)} & \textbf{平均路径长度(m)} & \textbf{平均完成时间(s)} & \textbf{约束违反次数} \\
\hline
\multirow{5}{*}{简单静态} & 传统DWA & 89.2 & 1245 & 89.3 & 0 \\
\cline{2-6}
& A*+DWA & 92.1 & 1178 & 84.7 & 0 \\
\cline{2-6}
& Pure TD3 & 85.4 & 1198 & 87.2 & 23 \\
\cline{2-6}
& PPO-Constrained & 88.7 & 1203 & 86.1 & 8 \\
\cline{2-6}
& DWA-TD3(本文) & 95.8 & 1142 & 81.5 & 0 \\
\hline
\multirow{5}{*}{中等动态} & 传统DWA & 74.6 & 1356 & 98.7 & 0 \\
\cline{2-6}
& A*+DWA & 79.3 & 1289 & 94.2 & 0 \\
\cline{2-6}
& Pure TD3 & 68.1 & 1387 & 102.5 & 45 \\
\cline{2-6}
& PPO-Constrained & 75.8 & 1324 & 97.8 & 12 \\
\cline{2-6}
& DWA-TD3(本文) & 87.9 & 1238 & 89.3 & 0 \\
\hline
\multirow{5}{*}{复杂密集} & 传统DWA & 52.8 & 1489 & 125.6 & 0 \\
\cline{2-6}
& A*+DWA & 63.2 & 1423 & 118.9 & 0 \\
\cline{2-6}
& Pure TD3 & 43.7 & 1534 & 135.2 & 78 \\
\cline{2-6}
& PPO-Constrained & 58.4 & 1467 & 127.3 & 28 \\
\cline{2-6}
& DWA-TD3(本文) & 75.6 & 1367 & 112.8 & 0 \\
\hline
\end{tabular}
\end{table}

实验结果表明：

1）\textbf{安全性保证}：本文方法与传统DWA和A*+DWA一样实现了零约束违反，而纯强化学习方法存在较多约束违反；

2）\textbf{路径效率}：相比传统方法，本文方法的平均路径长度减少8.2\%-12.5\%，完成时间缩短10.1\%-15.3\%；

3）\textbf{环境适应性}：随着环境复杂度增加，传统方法性能显著下降，而本文方法保持相对稳定的高性能。

\textbf{消融实验分析}

为验证算法各组件的贡献，进行了消融实验，结果如表8所示。

\begin{table}[htbp]
\centering
\caption{消融实验结果}
\begin{tabular}{|c|c|c|c|}
\hline
\textbf{算法变体} & \textbf{成功率(\%)} & \textbf{收敛episode数} & \textbf{平均路径长度(m)} \\
\hline
无DWA约束层 & 68.9 & 85000 & 1387 \\
\hline
无课程学习 & 81.2 & 65000 & 1298 \\
\hline
无双重Critic & 84.5 & 58000 & 1275 \\
\hline
无延迟更新 & 86.1 & 52000 & 1262 \\
\hline
完整方法 & 87.9 & 45000 & 1238 \\
\hline
\end{tabular}
\end{table}

消融实验表明DWA约束层对安全性和性能提升贡献最大，课程学习策略显著加速收敛，双重Critic和延迟更新机制进一步提升训练稳定性。

\textbf{计算效率分析}

表9展示了各算法的计算效率对比。

\begin{table}[htbp]
\centering
\caption{计算效率对比分析}
\begin{tabular}{|c|c|c|c|}
\hline
\textbf{算法} & \textbf{单步推理时间(ms)} & \textbf{内存占用(MB)} & \textbf{实时性} \\
\hline
传统DWA & 6.8 & 85 & 优秀 \\
\hline
A*+DWA & 125.3 & 156 & 一般 \\
\hline
Pure TD3 & 8.2 & 178 & 良好 \\
\hline
PPO-Constrained & 12.7 & 203 & 良好 \\
\hline
DWA-TD3(本文) & 9.4 & 192 & 良好 \\
\hline
\end{tabular}
\end{table}

本文方法的单步推理时间为9.4ms，满足巡飞弹实时控制要求（<10ms），相比A*+DWA方法效率提升92.5\%。

\subsection{\texorpdfstring{\textbf{5
结论}}{5 结论}}\label{ux7ed3ux8bba}

本文面向巡飞弹复杂动态环境下的自主运动规划问题，针对传统方法难以兼顾全局路径优化、实时安全约束与运动学可行性的挑战，提出融合DWA与CPO的协同规划框架，显著提升巡飞弹在动态障碍场景中的规划鲁棒性与任务执行效率。主要研究结论如下：

1）\textbf{构建DWA-CPO分层协同架构}，通过改进动态窗口法实时生成满足运动学约束（速度、加速度、转弯半径）与避障要求的安全控制输入集，为强化学习提供严格约束的动作空间；同时基于CPO框架实现全局策略优化，突破传统DWA的局部最优局限。\\
2）\textbf{设计多目标约束的TD3策略学习机制}，引入双重Critic网络减少价值估计偏差，结合延迟更新与目标策略平滑技术提升训练稳定性；创新设计能耗效率、路径平滑性及全局优化的多维度奖励函数，驱动智能体生成兼顾长期性能与运动学可行性的轨迹。\\
3）\textbf{提出分阶段课程学习策略}，从简单静态环境渐进过渡至密集动态场景（含移动障碍与突发威胁），通过环境复杂度自适应调整机制加速策略收敛，增强算法对未知动态环境的泛化能力。

总的来说，该框架为巡飞弹在复杂对抗环境中的高可靠自主决策提供了理论支撑与工程应用范式。

\textbf{参考文献（References）}

{[}1{]} CHENG N, WU S, WANG X C, et al. AI for UAV-assisted IoT
applications: a comprehensive review{[}J{]}. IEEE Internet of Things
Journal, 2023, 10(16): 14438-14461.

{[}2{]} 吴俊岐, 吴碧, 邓宏彬, 等.
基于动态参数扩展控制障碍函数的无人机时变编队最优跟踪控制{[}J{]}.
兵工学报, 2025, 46(4): 240260.

WU J Q, WU B, DENG H B,et al. Time-varying UAV formation optimal
tracking control with dynamic parameter extended control barrier
functions{[}J{]}. Acta Armamentarii, 2025, 46(4):240260. (in Chinese)

{[}3{]} CAO L, PAN Y N, LIANG H J, et al. Observer-based dynamic
event-triggered control for multiagent systems with time-varying
delay{[}J{]}. IEEE Transactions on Cybernetics, 2023, 53(5): 3376-3387.

{[}4{]} 王振威, 刘凯, 郭健, 等.
一种基于领导-跟随策略的多无人机-多无人艇编队协同机制{[}J{]}. 航空学报,
2023, 44(增刊2): 453-468.

WANG Z W, LIU K, GUO J, et al. A multi-UAVs and multi-USVs formation
cooperative mechanism based on leader-follower strategy{[}J{]}. Acta
Aeronautica et Astronautica Sinica, 2024, 44(S2): 453-468. (in Chinese)

{[}5{]} WANG J H, ALATTAS K A, BOUTERAA Y, et al. Adaptive finite-time
backstepping control tracker for quadrotor UAV with model uncertainty
and external disturbance{[}J{]}. Aerospace Science and Technology, 2023,
133:108088.

{[}6{]} MOFID O, MOBAYEN S, ZHANG C, et al. Desired tracking of delayed
quadrotor UAV under model uncertainty and wind disturbance using
adaptive super-twisting terminal sliding mode control{[}J{]}. Isa
Transactions, 2022, 123: 455-471.

{[}7{]} ALTAN A, HACIOGLU R. Model predictive control of three-axis
gimbal system mounted on UAV for real-time target tracking under
external disturbances{[}J{]}. Mechanical Systems and Signal Processing,
2020, 138: 106548.

{[}8{]} MANZOOR T, XIA Y Q, ZHAI D H, et al. Trajectory tracking control
of a VTOL unmanned aerial vehicle using offset-free tracking MPC{[}J{]}.
Chinese Journal of Aeronautics, 2020, 33(7): 2024-2042.

{[}9{]} LI Z, GUO Y L, WANG G, et al. Level curve tracking via robust
RL-guided model predictive control{[}J{]}. IEEE-CAA Journal of
Automatica Sinica, 2024, 11(12): 2512-2514.

{[}10{]} DONG F, LI X C, YOU K Y, et al. Standoff Tracking Using
DNN-Based MPC With Implementation on FPGA{[}J{]}. IEEE Transactions on
Control Systems Technology, 2023, 31(5): 1998-2010.

{[}11{]} KOELN J, RAGHURAMAN V, HENCEY B. Vertical hierarchical MPC for
constrained linear systems{[}J{]}. Automatica, 2020, 113: 108817.

{[}12{]} CHAI R, TSOURDOS A, GAO H J, et al. Attitude tracking control
for reentry vehicles using centralised robust model predictive
control{[}J{]}. Automatica, 2022, 145: 110561.

{[}13{]} 李曹妍, 郭振川, 郑冬冬, 等.
基于分布式模型预测控制的多机器人协同编队{[}J{]}. 兵工学报, 2023,
44(增刊2): 178-190.

LI C Y, GUO Z C, ZHENG D D, et al. Multi-robot cooperative formation
based on distributed model predictive control{[}J{]}. Acta Armamentarii,
2023, 44(S2): 178-190. (in Chinese)

{[}14{]} CAI Z H, WANG L H, ZHAO J, et al. Virtual target guidance-based
distributed model predictive control for formation control of multiple
UAVs{[}J{]}. Chinese Journal of Aeronautics, 2020, 33(3): 1037-1056.

{[}15{]} 赵超轮, 戴邵武, 何云风,等.
基于DMPC的多无人机轨迹跟踪与避碰切换控制{[}J{]}. 航空兵器, 2023,
30(6):64-74.

ZHAO C L, DAI S W, HE Y F, et al. Trajectory tracking and collision
avoidance switching control for multi-UAV based on DMPC{[}J{]}. Aero
Weaponry, 2023, 30(6): 64-74.(in Chinese)

{[}16{]} EL-SAYYAH M, SAAD M R, SAAD M. Enhanced MPC for omnidirectional
robot motion tracking using laguerre functions and non-iterative
linearization{[}J{]}. IEEE Access, 2022, 10: 118290-118301.

{[}17{]} ZHANG B, ZONG C F, CHEN G Y, et al. Electrical vehicle path
tracking based model predictive control with a Laguerre function and
exponential weight{[}J{]}. IEEE Access, 2019, 7: 17082-17097.

{[}18{]} WANG W R, YAN J H, WANG H, et al. Adaptive MPC trajectory
tracking for AUV based on Laguerre function{[}J{]}. Ocean Engineering,
2022, 261.

{[}19{]} RESMI R, MIJA S J, JACOB J. Discrete Laguerre-based model
predictive control for dynamic consensus of a vehicle platoon with time
delay{[}J{]}. International Journal of Systems Science, 2022, 53(12):
2566-2583.

{[}20{]} WANG M Z, ZHAO C C, XIA J H, et al. Periodic event-triggered
robust distributed model predictive control for multiagent systems with
input and communication delays{[}J{]}. IEEE Transactions on Industrial
Informatics, 2023, 19(11): 11216-11228.

{[}21{]} KOBAYASHI K, HIRAISHI K. Self-triggered model predictive
control with delay compensation for networked control
systems{[}C{]}//Proceedings of the 38th Annual Conference on
IEEE-Industrial-Electronics-Society . Montreal, QC, Canada:IEEE, 2012:
3200-3205.

{[}22{]} ZHOU Z, LIU Z T, SU H Y, et al. Model predictive control with
fractional-order delay compensation for fast sampling systems{[}J{]}.
Science China-Information Sciences, 2021, 64:172211.

{[}23{]} 徐广通, 王祝, 曹严, 等.
动态优先级解耦的无人机集群轨迹分布式序列凸规划{[}J{]}. 航空学报, 2022,
43(2): 420-431.

XU G T, WANG Z, CAO Y, et al. Dymamic-priority decoupled UAV swarm
trajectory plamming using distributed sequential convex{[}J{]}. Acta
Aeronautica et Astronautica Sinica, 2022,43(2): 420-431. (in Chinese)

\end{document}
