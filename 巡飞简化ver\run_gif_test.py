"""
训练结果GIF测试主程序
提供简单的阶段选择和测试功能
"""

import os
import sys
import subprocess
from datetime import datetime

def main():
    print("🎬 训练结果GIF生成器 - 主程序")
    print("=" * 60)
    print(f"📅 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"⚙️  默认设置: 最大步数=1500, 帧率=12fps")
    print()
    
    # 检查训练目录
    target_dir = "./loitering_munition_staged_training_20250726_085626"
    if not os.path.exists(target_dir):
        print("❌ 未找到训练结果目录")
        print(f"   期望路径: {target_dir}")
        return False
    
    print(f"✅ 找到训练目录: {target_dir}")
    
    # 检查模型文件
    files = os.listdir(target_dir)
    model_files = [f for f in files if f.endswith('.pth')]
    print(f"📦 找到模型文件: {model_files}")
    
    # 显示阶段选择
    print("\n🎯 可用的训练阶段测试:")
    print("=" * 50)
    
    stage_info = {
        1: {"name": "阶段1 - 简单环境基础训练", "success_rate": "98.3%", "env": "简单静态障碍物", "script": "test_stage1.py"},
        2: {"name": "阶段2 - 复杂静态环境训练", "success_rate": "91.6%", "env": "高密度静态障碍物", "script": "test_stage2.py"},
        3: {"name": "阶段3 - 动态环境适应训练", "success_rate": "4.0%", "env": "动态障碍物环境", "script": "test_stage3.py"}
    }
    
    available_stages = []
    for i in range(1, 4):
        if f'stage_{i}_model.pth' in model_files:
            available_stages.append(i)
            info = stage_info[i]
            print(f"  {i}. {info['name']}")
            print(f"     成功率: {info['success_rate']} | 环境: {info['env']}")
            print(f"     运行命令: python {info['script']}")
            print()
    
    print("💡 推荐测试顺序:")
    print("   1. 先测试阶段1 (成功率最高，效果最好)")
    print("   2. 再测试阶段2 (有一定挑战性)")
    print("   3. 最后测试阶段3 (预期失败，观察行为)")
    print()
    
    print("🚀 快速启动命令:")
    for stage in available_stages:
        info = stage_info[stage]
        print(f"   python {info['script']}  # {info['name']}")
    
    print("\n📋 手动命令 (更多控制):")
    for stage in available_stages:
        print(f"   python test_training_gif_generator.py --stage {stage} --steps 1500")
    
    print("\n" + "=" * 60)
    print("🎉 请选择上述任一命令运行，开始GIF生成测试!")
    print("💡 建议从阶段1开始，因为它的成功率最高")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print(f"\n❌ 初始化失败")
    except KeyboardInterrupt:
        print(f"\n👋 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序运行出现错误: {e}")
        import traceback
        traceback.print_exc()
