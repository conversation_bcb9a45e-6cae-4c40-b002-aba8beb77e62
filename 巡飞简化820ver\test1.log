This is XeTeX, Version 3.141592653-2.6-0.999996 (TeX Live 2024) (preloaded format=xelatex 2024.11.28)  15 AUG 2025 15:18
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**test1.tex
(./test1.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(e:/texlive/2024/texmf-dist/tex/latex/ctex/ctexart.cls
(e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctexbackend.cfg
File: ctexbackend.cfg 2022/07/14 v2.5.10 Backend configuration file (CTEX)
)
(e:/texlive/2024/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 

(e:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-02-20 L3 backend support: XeTeX
\g__graphics_track_int=\count184
\l__pdf_internal_box=\box51
\g__pdf_backend_object_int=\count185
\g__pdf_backend_annotation_int=\count186
\g__pdf_backend_link_int=\count187
))
Document Class: ctexart 2022/07/14 v2.5.10 Chinese adapter for class article (C
TEX)
(e:/texlive/2024/texmf-dist/tex/latex/ctex/ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
)
(e:/texlive/2024/texmf-dist/tex/latex/ctex/ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
)
(e:/texlive/2024/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX

(e:/texlive/2024/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\l__ctex_tmp_int=\count188
\l__ctex_tmp_box=\box52
\l__ctex_tmp_dim=\dimen140
\g__ctex_section_depth_int=\count189
\g__ctex_font_size_int=\count190

(e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctexopts.cfg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
)
(e:/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(e:/texlive/2024/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count191
\c@section=\count192
\c@subsection=\count193
\c@subsubsection=\count194
\c@paragraph=\count195
\c@subparagraph=\count196
\c@figure=\count197
\c@table=\count198
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen141
)
(e:/texlive/2024/texmf-dist/tex/latex/ctex/engine/ctex-engine-xetex.def
File: ctex-engine-xetex.def 2022/07/14 v2.5.10 XeLaTeX adapter (CTEX)

(e:/texlive/2024/texmf-dist/tex/xelatex/xecjk/xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX

(e:/texlive/2024/texmf-dist/tex/latex/l3packages/xtemplate/xtemplate.sty
(e:/texlive/2024/texmf-dist/tex/latex/l3packages/xtemplate/xtemplate-2023-10-10
.sty
Package: xtemplate 2023-10-10 L3 Experimental prototype document functions
\l__xtemplate_tmp_dim=\dimen142
\l__xtemplate_tmp_int=\count199
\l__xtemplate_tmp_muskip=\muskip16
\l__xtemplate_tmp_skip=\skip50
))
\l__xeCJK_tmp_int=\count266
\l__xeCJK_tmp_box=\box53
\l__xeCJK_tmp_dim=\dimen143
\l__xeCJK_tmp_skip=\skip51
\g__xeCJK_space_factor_int=\count267
\l__xeCJK_begin_int=\count268
\l__xeCJK_end_int=\count269
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip52
\c__xeCJK_none_node=\count270
\g__xeCJK_node_int=\count271
\c__xeCJK_CJK_node_dim=\dimen144
\c__xeCJK_CJK-space_node_dim=\dimen145
\c__xeCJK_default_node_dim=\dimen146
\c__xeCJK_CJK-widow_node_dim=\dimen147
\c__xeCJK_normalspace_node_dim=\dimen148
\c__xeCJK_default-space_node_skip=\skip53
\l__xeCJK_ccglue_skip=\skip54
\l__xeCJK_ecglue_skip=\skip55
\l__xeCJK_punct_kern_skip=\skip56
\l__xeCJK_indent_box=\box54
\l__xeCJK_last_penalty_int=\count272
\l__xeCJK_last_bound_dim=\dimen149
\l__xeCJK_last_kern_dim=\dimen150
\l__xeCJK_widow_penalty_int=\count273

Package xtemplate Info: Declaring object type 'xeCJK/punctuation' taking 0
(xtemplate)             argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen151
\l__xeCJK_mixed_punct_width_dim=\dimen152
\l__xeCJK_middle_punct_width_dim=\dimen153
\l__xeCJK_fixed_margin_width_dim=\dimen154
\l__xeCJK_mixed_margin_width_dim=\dimen155
\l__xeCJK_middle_margin_width_dim=\dimen156
\l__xeCJK_bound_punct_width_dim=\dimen157
\l__xeCJK_bound_margin_width_dim=\dimen158
\l__xeCJK_margin_minimum_dim=\dimen159
\l__xeCJK_kerning_total_width_dim=\dimen160
\l__xeCJK_same_align_margin_dim=\dimen161
\l__xeCJK_different_align_margin_dim=\dimen162
\l__xeCJK_kerning_margin_width_dim=\dimen163
\l__xeCJK_kerning_margin_minimum_dim=\dimen164
\l__xeCJK_bound_dim=\dimen165
\l__xeCJK_reverse_bound_dim=\dimen166
\l__xeCJK_margin_dim=\dimen167
\l__xeCJK_minimum_bound_dim=\dimen168
\l__xeCJK_kerning_margin_dim=\dimen169
\g__xeCJK_family_int=\count274
\l__xeCJK_fam_int=\count275
\g__xeCJK_fam_allocation_int=\count276
\l__xeCJK_verb_case_int=\count277
\l__xeCJK_verb_exspace_skip=\skip57
 (e:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.sty
(e:/texlive/2024/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-02-18 L3 Experimental document command parser
)
Package: fontspec 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTeX

(e:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTe
X
\l__fontspec_script_int=\count278
\l__fontspec_language_int=\count279
\l__fontspec_strnum_int=\count280
\l__fontspec_tmp_int=\count281
\l__fontspec_tmpa_int=\count282
\l__fontspec_tmpb_int=\count283
\l__fontspec_tmpc_int=\count284
\l__fontspec_em_int=\count285
\l__fontspec_emdef_int=\count286
\l__fontspec_strong_int=\count287
\l__fontspec_strongdef_int=\count288
\l__fontspec_tmpa_dim=\dimen170
\l__fontspec_tmpb_dim=\dimen171
\l__fontspec_tmpc_dim=\dimen172

(e:/texlive/2024/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(e:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.cfg)))
(e:/texlive/2024/texmf-dist/tex/xelatex/xecjk/xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
))
\ccwd=\dimen173
\l__ctex_ccglue_skip=\skip58
)
\l__ctex_ziju_dim=\dimen174

(e:/texlive/2024/texmf-dist/tex/latex/zhnumber/zhnumber.sty
Package: zhnumber 2022/07/14 v3.0 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count289
\l__zhnum_tmp_int=\count290

(e:/texlive/2024/texmf-dist/tex/latex/zhnumber/zhnumber-utf8.cfg
File: zhnumber-utf8.cfg 2022/07/14 v3.0 Chinese numerals with UTF8 encoding
))
\l__ctex_heading_skip=\skip59

(e:/texlive/2024/texmf-dist/tex/latex/ctex/scheme/ctex-scheme-chinese-article.d
ef
File: ctex-scheme-chinese-article.def 2022/07/14 v2.5.10 Chinese scheme for art
icle (CTEX)
 (e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctex-name-utf8.cfg
File: ctex-name-utf8.cfg 2022/07/14 v2.5.10 Caption with encoding UTF-8 (CTEX)
))
(e:/texlive/2024/texmf-dist/tex/latex/ctex/ctex-c5size.clo
File: ctex-c5size.clo 2022/07/14 v2.5.10 c5size option (CTEX)
)
(e:/texlive/2024/texmf-dist/tex/latex/ctex/fontset/ctex-fontset-windows.def
File: ctex-fontset-windows.def 2022/07/14 v2.5.10 Windows fonts definition (CTE
X)

Package fontspec Info: Could not resolve font "KaiTi/B" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimHei/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimSun/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Font family 'SimSun(0)' created for font 'SimSun' with
(fontspec)             options
(fontspec)             [Script={CJK},BoldFont={SimHei},ItalicFont={KaiTi}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"SimSun/OT:script=hani;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"SimHei/OT:script=hani;language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"KaiTi/OT:script=hani;language=dflt;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.: 

))
(e:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctex.cfg
File: ctex.cfg 2022/07/14 v2.5.10 Configuration file (CTEX)
)

Package xeCJK Warning: Redefining CJKfamily `\CJKrmdefault' (SimSun(0)).


Package fontspec Info: Could not resolve font "SimSun/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimSun/B" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimSun/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Font family 'SimSun(1)' created for font 'SimSun' with
(fontspec)             options [Script={CJK}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"SimSun/OT:script=hani;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 


Package xeCJK Warning: Redefining CJKfamily `\CJKsfdefault' (Microsoft YaHei).

(e:/texlive/2024/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2021/03/15 v2.8 Span multiple rows of a table
\multirow@colwidth=\skip60
\multirow@cntb=\count291
\multirow@dima=\skip61
\bigstrutjot=\dimen175
)
(e:/texlive/2024/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen176
\ar@mcellbox=\box55
\extrarowheight=\dimen177
\NC@list=\toks17
\extratabsurround=\skip62
\backup@length=\skip63
\ar@cellbox=\box56
)
(e:/texlive/2024/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(e:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.

(e:/texlive/2024/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
)
(e:/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
(e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip64

For additional information on amsmath, use the `?' option.
(e:/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen178
))
(e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen179
)
(e:/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count292
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count293
\leftroot@=\count294
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count295
\DOTSCASE@=\count296
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box57
\strutbox@=\box58
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen180
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count297
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count298
\dotsspace@=\muskip17
\c@parentequation=\count299
\dspbrk@lvl=\count300
\tag@help=\toks19
\row@=\count301
\column@=\count302
\maxfields@=\count303
\andhelp@=\toks20
\eqnshift@=\dimen181
\alignsep@=\dimen182
\tagshift@=\dimen183
\tagwidth@=\dimen184
\totwidth@=\dimen185
\lineht@=\dimen186
\@envbody=\toks21
\multlinegap=\skip65
\multlinetaggap=\skip66
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(e:/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(e:/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(e:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)
(e:/texlive/2024/texmf-dist/tex/latex/unicode-math/unicode-math.sty
Package: unicode-math 2023/08/13 v0.8r Unicode maths in XeLaTeX and LuaLaTeX

(e:/texlive/2024/texmf-dist/tex/latex/unicode-math/unicode-math-xetex.sty
Package: unicode-math-xetex 2023/08/13 v0.8r Unicode maths in XeLaTeX and LuaLa
TeX

(e:/texlive/2024/texmf-dist/tex/latex/l3packages/l3keys2e/l3keys2e.sty
Package: l3keys2e 2024-02-18 LaTeX2e option processing using LaTeX3 keys
)
\g__um_fam_int=\count304
\g__um_fonts_used_int=\count305
\l__um_primecount_int=\count306
\g__um_primekern_muskip=\muskip18

(e:/texlive/2024/texmf-dist/tex/latex/unicode-math/unicode-math-table.tex)))
(e:/texlive/2024/texmf-dist/tex/latex/lm/lmodern.sty
Package: lmodern 2015/05/01 v1.6.1 Latin Modern Fonts
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/lmr/m/n on input line 22.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/lmm/m/it on input line 23.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> OMS/lmsy/m/n on input line 24.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> OMX/lmex/m/n on input line 25.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 26.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/lmm/b/it on input line 27.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> OMS/lmsy/b/n on input line 28.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> OMX/lmex/m/n on input line 29.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/lmss/m/n on input line 32.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/lmr/m/it on input line 33.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> OT1/lmtt/m/n on input line 34.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/lmss/bx/n on input line 36.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/lmr/bx/it on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> OT1/lmtt/m/n on input line 38.
)
(e:/texlive/2024/texmf-dist/tex/latex/upquote/upquote.sty
Package: upquote 2012/04/19 v1.3 upright-quote and grave-accent glyphs in verba
tim

(e:/texlive/2024/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
))
(e:/texlive/2024/texmf-dist/tex/latex/microtype/microtype.sty
Package: microtype 2023/03/13 v3.1a Micro-typographical refinements (RS)

(e:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks23
)
(e:/texlive/2024/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count307
)
\MT@toks=\toks24
\MT@tempbox=\box59
\MT@count=\count308
LaTeX Info: Redefining \noprotrusionifhmode on input line 1059.
LaTeX Info: Redefining \leftprotrusion on input line 1060.
\MT@prot@toks=\toks25
LaTeX Info: Redefining \rightprotrusion on input line 1078.
LaTeX Info: Redefining \textls on input line 1368.
\MT@outer@kern=\dimen187
LaTeX Info: Redefining \textmicrotypecontext on input line 1988.
\MT@listname@count=\count309

(e:/texlive/2024/texmf-dist/tex/latex/microtype/microtype-xetex.def
File: microtype-xetex.def 2023/03/13 v3.1a Definitions specific to xetex (RS)
LaTeX Info: Redefining \lsstyle on input line 238.
)
Package microtype Info: Loading configuration file microtype.cfg.

(e:/texlive/2024/texmf-dist/tex/latex/microtype/microtype.cfg
File: microtype.cfg 2023/03/13 v3.1a microtype main configuration file (RS)
))
(e:/texlive/2024/texmf-dist/tex/latex/parskip/parskip.sty
Package: parskip 2021-03-14 v2.0h non-zero parskip adjustments

(e:/texlive/2024/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)

(e:/texlive/2024/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(e:/texlive/2024/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))
Couldn't patch \@startsection
Couldn't patch \@xsect
) (e:/texlive/2024/texmf-dist/tex/latex/tools/longtable.sty
Package: longtable 2023-11-01 v4.19 Multi-page Table package (DPC)
\LTleft=\skip67
\LTright=\skip68
\LTpre=\skip69
\LTpost=\skip70
\LTchunksize=\count310
\LTcapwidth=\dimen188
\LT@head=\box60
\LT@firsthead=\box61
\LT@foot=\box62
\LT@lastfoot=\box63
\LT@gbox=\box64
\LT@cols=\count311
\LT@rows=\count312
\c@LT@tables=\count313
\c@LT@chunks=\count314
\LT@p@ftn=\toks26
)
(e:/texlive/2024/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen189
\lightrulewidth=\dimen190
\cmidrulewidth=\dimen191
\belowrulesep=\dimen192
\belowbottomsep=\dimen193
\aboverulesep=\dimen194
\abovetopsep=\dimen195
\cmidrulesep=\dimen196
\cmidrulekern=\dimen197
\defaultaddspace=\dimen198
\@cmidla=\count315
\@cmidlb=\count316
\@aboverulesep=\dimen199
\@belowrulesep=\dimen256
\@thisruleclass=\count317
\@lastruleclass=\count318
\@thisrulewidth=\dimen257
)
(e:/texlive/2024/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count319
\calc@Bcount=\count320
\calc@Adimen=\dimen258
\calc@Bdimen=\dimen259
\calc@Askip=\skip71
\calc@Bskip=\skip72
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count321
\calc@Cskip=\skip73
)
(e:/texlive/2024/texmf-dist/tex/latex/footnotehyper/footnotehyper.sty
Package: footnotehyper 2021/08/13 v1.1e hyperref aware footnote.sty (JFB)
\FNH@notes=\box65
\FNH@width=\dimen260
\FNH@toks=\toks27
)
(e:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(e:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)

(e:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(e:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 107.
)
\Gin@req@height=\dimen261
\Gin@req@width=\dimen262
)
\pandoc@box=\box66

(e:/texlive/2024/texmf-dist/tex/generic/soul/soul.sty
Package: soul 2023-06-14 v3.1 Permit use of UTF-8 characters in soul (HO)

(e:/texlive/2024/texmf-dist/tex/generic/soul/soul-ori.sty
Package: soul-ori 2023-06-14 v3.1 letterspacing/underlining (mf)
\SOUL@word=\toks28
\SOUL@lasttoken=\toks29
\SOUL@syllable=\toks30
\SOUL@cmds=\toks31
\SOUL@buffer=\toks32
\SOUL@token=\toks33
\SOUL@syllgoal=\dimen263
\SOUL@syllwidth=\dimen264
\SOUL@charkern=\dimen265
\SOUL@hyphkern=\dimen266
\SOUL@dimen=\dimen267
\SOUL@dimeni=\dimen268
\SOUL@minus=\count322
\SOUL@comma=\count323
\SOUL@apo=\count324
\SOUL@grave=\count325
\SOUL@spaceskip=\skip74
\SOUL@ttwidth=\dimen269
\SOUL@uldp=\dimen270
\SOUL@ulht=\dimen271
)
(e:/texlive/2024/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
(e:/texlive/2024/texmf-dist/tex/generic/etexcmds/etexcmds.sty
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)
))
(e:/texlive/2024/texmf-dist/tex/latex/bookmark/bookmark.sty
Package: bookmark 2023-12-10 v1.31 PDF bookmarks (HO)

(e:/texlive/2024/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-01-20 v7.01h Hypertext links for LaTeX

(e:/texlive/2024/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(e:/texlive/2024/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(e:/texlive/2024/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
))
(e:/texlive/2024/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(e:/texlive/2024/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
)
(e:/texlive/2024/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(e:/texlive/2024/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(e:/texlive/2024/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count326
)
\@linkdim=\dimen272
\Hy@linkcounter=\count327
\Hy@pagecounter=\count328

(e:/texlive/2024/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-01-20 v7.01h Hyperref: PDFDocEncoding definition (HO)
)
(e:/texlive/2024/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count329

(e:/texlive/2024/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-01-20 v7.01h Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `unicode' set `true' on input line 4062.
Package hyperref Info: Option `unicode' set `true' on input line 4062.
Package hyperref Info: Hyper figures OFF on input line 4179.
Package hyperref Info: Link nesting OFF on input line 4184.
Package hyperref Info: Hyper index ON on input line 4187.
Package hyperref Info: Plain pages OFF on input line 4194.
Package hyperref Info: Backreferencing OFF on input line 4199.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4446.
\c@Hy@tempcnt=\count330

(e:/texlive/2024/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip19
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4784.
\XeTeXLinkMargin=\dimen273

(e:/texlive/2024/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(e:/texlive/2024/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count331
\Field@Width=\dimen274
\Fld@charsize=\dimen275
Package hyperref Info: Hyper figures OFF on input line 6063.
Package hyperref Info: Link nesting OFF on input line 6068.
Package hyperref Info: Hyper index ON on input line 6071.
Package hyperref Info: backreferencing OFF on input line 6078.
Package hyperref Info: Link coloring OFF on input line 6083.
Package hyperref Info: Link coloring with OCG OFF on input line 6088.
Package hyperref Info: PDF/A mode OFF on input line 6093.

(e:/texlive/2024/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count332
\c@Item=\count333
\c@Hfootnote=\count334
)
Package hyperref Info: Driver (autodetected): hxetex.

(e:/texlive/2024/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2024-01-20 v7.01h Hyperref driver for XeTeX

(e:/texlive/2024/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\pdfm@box=\box67
\c@Hy@AnnotLevel=\count335
\HyField@AnnotCount=\count336
\Fld@listcount=\count337
\c@bookmark@seq@number=\count338

(e:/texlive/2024/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(e:/texlive/2024/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
(e:/texlive/2024/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip75
)
(e:/texlive/2024/texmf-dist/tex/latex/bookmark/bkm-dvipdfm.def
File: bkm-dvipdfm.def 2023-12-10 v1.31 bookmark driver for dvipdfm (HO)
\BKM@id=\count339
))
(e:/texlive/2024/texmf-dist/tex/latex/xurl/xurl.sty
Package: xurl 2022/01/09 v 0.10 modify URL breaks
) (./test1.aux)
\openout1 = `test1.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 93.
LaTeX Font Info:    ... okay on input line 93.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 93.
LaTeX Font Info:    ... okay on input line 93.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 93.
LaTeX Font Info:    ... okay on input line 93.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 93.
LaTeX Font Info:    ... okay on input line 93.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 93.
LaTeX Font Info:    ... okay on input line 93.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 93.
LaTeX Font Info:    ... okay on input line 93.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 93.
LaTeX Font Info:    ... okay on input line 93.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 93.
LaTeX Font Info:    ... okay on input line 93.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 93.
LaTeX Font Info:    ... okay on input line 93.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 93.
LaTeX Font Info:    ... okay on input line 93.
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `normal'
(Font)                  OT1/lmr/m/n --> TU/lmr/m/n on input line 93.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/lmr/m/it --> TU/lmr/m/it on input line 93.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/lmr/bx/n --> TU/lmr/bx/n on input line 93.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/lmss/m/n --> TU/lmss/m/n on input line 93.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/lmss/bx/n --> TU/lmss/bx/n on input line 93.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/lmtt/m/n --> TU/lmtt/m/n on input line 93.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/lmtt/m/n --> TU/lmtt/bx/n on input line 93.

Package fontspec Info: latinmodern-math scale = 0.9999966408685371.


Package fontspec Info: latinmodern-math scale = 0.9999966408685371.


Package fontspec Info: latinmodern-math scale = 0.9999966408685371.


Package fontspec Info: Font family 'latinmodern-math.otf(0)' created for font
(fontspec)             'latinmodern-math.otf' with options
(fontspec)             [Scale=MatchLowercase,BoldItalicFont={},ItalicFont={},Sm
allCapsFont={},Script=Math,BoldFont={latinmodern-math.otf}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->s*[0.9999966408685371]"[latinmodern-math.otf]/OT:scri
pt=math;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[0.9999966408685371]"[latinmodern-math.otf]/OT:scri
pt=math;language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 

LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(0)/m/n' will be
(Font)              scaled to size 10.53937pt on input line 93.

Package fontspec Info: latinmodern-math scale = 0.9999966408685371.


Package fontspec Info: latinmodern-math scale = 0.9999966408685371.


Package fontspec Info: latinmodern-math scale = 0.9999966408685371.


Package fontspec Info: latinmodern-math scale = 0.9999966408685371.


Package fontspec Info: latinmodern-math scale = 0.9999966408685371.


Package fontspec Info: Font family 'latinmodern-math.otf(1)' created for font
(fontspec)             'latinmodern-math.otf' with options
(fontspec)             [Scale=MatchLowercase,BoldItalicFont={},ItalicFont={},Sm
allCapsFont={},Script=Math,SizeFeatures={{Size=8.9584645-},{Size=6.323622-8.958
4645,Font=latinmodern-math.otf,Style=MathScript},{Size=-6.323622,Font=latinmode
rn-math.otf,Style=MathScriptScript}},BoldFont={latinmodern-math.otf}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <8.9584645->s*[0.9999966408685371]"[latinmodern-math.otf
]/OT:script=math;language=dflt;"<6.323622-8.9584645>s*[0.9999966408685371]"[lat
inmodern-math.otf]/OT:script=math;language=dflt;+ssty=0;"<-6.323622>s*[0.999996
6408685371]"[latinmodern-math.otf]/OT:script=math;language=dflt;+ssty=1;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[0.9999966408685371]"[latinmodern-math.otf]/OT:scri
pt=math;language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 

LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(1)/m/n' will be
(Font)              scaled to size 10.53937pt on input line 93.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 93.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/lmr/m/n --> TU/latinmodern-math.otf(1)/m/n on input
 line 93.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 93.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/lmr/bx/n --> TU/latinmodern-math.otf(1)/b/n on inpu
t line 93.

Package fontspec Info: latinmodern-math scale = 0.9999966408685371.


Package fontspec Info: latinmodern-math scale = 1.000096640532624.


Package fontspec Info: latinmodern-math scale = 0.9999966408685371.


Package fontspec Info: latinmodern-math scale = 1.000096640532624.


Package fontspec Info: latinmodern-math scale = 0.9999966408685371.


Package fontspec Info: latinmodern-math scale = 1.000096640532624.


Package fontspec Info: latinmodern-math scale = 0.9999966408685371.


Package fontspec Info: latinmodern-math scale = 1.000096640532624.


Package fontspec Info: latinmodern-math scale = 0.9999966408685371.


Package fontspec Info: latinmodern-math scale = 1.000096640532624.


Package fontspec Info: Font family 'latinmodern-math.otf(2)' created for font
(fontspec)             'latinmodern-math.otf' with options
(fontspec)             [Scale=MatchLowercase,BoldItalicFont={},ItalicFont={},Sm
allCapsFont={},Script=Math,SizeFeatures={{Size=8.9584645-},{Size=6.323622-8.958
4645,Font=latinmodern-math.otf,Style=MathScript},{Size=-6.323622,Font=latinmode
rn-math.otf,Style=MathScriptScript}},BoldFont={latinmodern-math.otf},ScaleAgain
=1.0001,FontAdjustment={\fontdimen
(fontspec)             8\font =7.13515pt\relax \fontdimen 9\font
(fontspec)             =4.15251pt\relax \fontdimen 10\font =4.67947pt\relax
(fontspec)             \fontdimen 11\font =7.23001pt\relax \fontdimen 12\font
(fontspec)             =3.63608pt\relax \fontdimen 13\font =3.82579pt\relax
(fontspec)             \fontdimen 14\font =3.82579pt\relax \fontdimen 15\font
(fontspec)             =3.04588pt\relax \fontdimen 16\font =2.60323pt\relax
(fontspec)             \fontdimen 17\font =2.60323pt\relax \fontdimen 18\font
(fontspec)             =2.63484pt\relax \fontdimen 19\font =2.10788pt\relax
(fontspec)             \fontdimen 22\font =2.63484pt\relax \fontdimen 20\font
(fontspec)             =0pt\relax \fontdimen 21\font =0pt\relax }].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <8.9584645->s*[1.000096640532624]"[latinmodern-math.otf]
/OT:script=math;language=dflt;"<6.323622-8.9584645>s*[1.000096640532624]"[latin
modern-math.otf]/OT:script=math;language=dflt;+ssty=0;"<-6.323622>s*[1.00009664
0532624]"[latinmodern-math.otf]/OT:script=math;language=dflt;+ssty=1;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             and font adjustment code:
(fontspec)             \fontdimen 8\font =7.13515pt\relax \fontdimen 9\font
(fontspec)             =4.15251pt\relax \fontdimen 10\font =4.67947pt\relax
(fontspec)             \fontdimen 11\font =7.23001pt\relax \fontdimen 12\font
(fontspec)             =3.63608pt\relax \fontdimen 13\font =3.82579pt\relax
(fontspec)             \fontdimen 14\font =3.82579pt\relax \fontdimen 15\font
(fontspec)             =3.04588pt\relax \fontdimen 16\font =2.60323pt\relax
(fontspec)             \fontdimen 17\font =2.60323pt\relax \fontdimen 18\font
(fontspec)             =2.63484pt\relax \fontdimen 19\font =2.10788pt\relax
(fontspec)             \fontdimen 22\font =2.63484pt\relax \fontdimen 20\font
(fontspec)             =0pt\relax \fontdimen 21\font =0pt\relax 
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[1.000096640532624]"[latinmodern-math.otf]/OT:scrip
t=math;language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 
(fontspec)             and font adjustment code:
(fontspec)             \fontdimen 8\font =7.13515pt\relax \fontdimen 9\font
(fontspec)             =4.15251pt\relax \fontdimen 10\font =4.67947pt\relax
(fontspec)             \fontdimen 11\font =7.23001pt\relax \fontdimen 12\font
(fontspec)             =3.63608pt\relax \fontdimen 13\font =3.82579pt\relax
(fontspec)             \fontdimen 14\font =3.82579pt\relax \fontdimen 15\font
(fontspec)             =3.04588pt\relax \fontdimen 16\font =2.60323pt\relax
(fontspec)             \fontdimen 17\font =2.60323pt\relax \fontdimen 18\font
(fontspec)             =2.63484pt\relax \fontdimen 19\font =2.10788pt\relax
(fontspec)             \fontdimen 22\font =2.63484pt\relax \fontdimen 20\font
(fontspec)             =0pt\relax \fontdimen 21\font =0pt\relax 

LaTeX Font Info:    Encoding `OMS' has changed to `TU' for symbol font
(Font)              `symbols' in the math version `normal' on input line 93.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/lmsy/m/n --> TU/latinmodern-math.otf(2)/m/n on inpu
t line 93.
LaTeX Font Info:    Encoding `OMS' has changed to `TU' for symbol font
(Font)              `symbols' in the math version `bold' on input line 93.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/lmsy/b/n --> TU/latinmodern-math.otf(2)/b/n on inpu
t line 93.

Package fontspec Info: latinmodern-math scale = 0.9999966408685371.


Package fontspec Info: latinmodern-math scale = 0.9998966412044502.


Package fontspec Info: latinmodern-math scale = 0.9999966408685371.


Package fontspec Info: latinmodern-math scale = 0.9998966412044502.


Package fontspec Info: latinmodern-math scale = 0.9999966408685371.


Package fontspec Info: latinmodern-math scale = 0.9998966412044502.


Package fontspec Info: latinmodern-math scale = 0.9999966408685371.


Package fontspec Info: latinmodern-math scale = 0.9998966412044502.


Package fontspec Info: latinmodern-math scale = 0.9999966408685371.


Package fontspec Info: latinmodern-math scale = 0.9998966412044502.


Package fontspec Info: Font family 'latinmodern-math.otf(3)' created for font
(fontspec)             'latinmodern-math.otf' with options
(fontspec)             [Scale=MatchLowercase,BoldItalicFont={},ItalicFont={},Sm
allCapsFont={},Script=Math,SizeFeatures={{Size=8.9584645-},{Size=6.323622-8.958
4645,Font=latinmodern-math.otf,Style=MathScript},{Size=-6.323622,Font=latinmode
rn-math.otf,Style=MathScriptScript}},BoldFont={latinmodern-math.otf},ScaleAgain
=0.9999,FontAdjustment={\fontdimen
(fontspec)             8\font =0.42157pt\relax \fontdimen 9\font
(fontspec)             =2.10788pt\relax \fontdimen 10\font =1.76007pt\relax
(fontspec)             \fontdimen 11\font =1.16988pt\relax \fontdimen 12\font
(fontspec)             =6.32362pt\relax \fontdimen 13\font =0pt\relax }].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <8.9584645->s*[0.9998966412044502]"[latinmodern-math.otf
]/OT:script=math;language=dflt;"<6.323622-8.9584645>s*[0.9998966412044502]"[lat
inmodern-math.otf]/OT:script=math;language=dflt;+ssty=0;"<-6.323622>s*[0.999896
6412044502]"[latinmodern-math.otf]/OT:script=math;language=dflt;+ssty=1;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             and font adjustment code:
(fontspec)             \fontdimen 8\font =0.42157pt\relax \fontdimen 9\font
(fontspec)             =2.10788pt\relax \fontdimen 10\font =1.76007pt\relax
(fontspec)             \fontdimen 11\font =1.16988pt\relax \fontdimen 12\font
(fontspec)             =6.32362pt\relax \fontdimen 13\font =0pt\relax 
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[0.9998966412044502]"[latinmodern-math.otf]/OT:scri
pt=math;language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 
(fontspec)             and font adjustment code:
(fontspec)             \fontdimen 8\font =0.42157pt\relax \fontdimen 9\font
(fontspec)             =2.10788pt\relax \fontdimen 10\font =1.76007pt\relax
(fontspec)             \fontdimen 11\font =1.16988pt\relax \fontdimen 12\font
(fontspec)             =6.32362pt\relax \fontdimen 13\font =0pt\relax 

LaTeX Font Info:    Encoding `OMX' has changed to `TU' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 9
3.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/lmex/m/n --> TU/latinmodern-math.otf(3)/m/n on inpu
t line 93.
LaTeX Font Info:    Encoding `OMX' has changed to `TU' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 93.

LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/lmex/m/n --> TU/latinmodern-math.otf(3)/b/n on inpu
t line 93.
LaTeX Info: Redefining \microtypecontext on input line 93.
Package microtype Info: Applying patch `item' on input line 93.
Package microtype Info: Applying patch `toc' on input line 93.
Package microtype Info: Applying patch `eqnum' on input line 93.
Package microtype Info: Applying patch `footnote' on input line 93.
Package microtype Info: Applying patch `verbatim' on input line 93.
Package microtype Info: Character protrusion enabled (level 2).
Package microtype Info: Using protrusion set `basicmath'.
Package microtype Info: No adjustment of tracking.
Package microtype Info: No adjustment of spacing.
Package microtype Info: No adjustment of kerning.

(e:/texlive/2024/texmf-dist/tex/latex/microtype/mt-LatinModernRoman.cfg
File: mt-LatinModernRoman.cfg 2021/02/21 v1.1 microtype config. file: Latin Mod
ern Roman (RS)
)
Package hyperref Info: Link coloring OFF on input line 93.


LaTeX Font Warning: Font shape `TU/SimSun(1)/b/n' undefined
(Font)              using `TU/SimSun(1)/m/n' instead on input line 100.

LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(1)/m/n' will be
(Font)              scaled to size 7.37756pt on input line 113.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(1)/m/n' will be
(Font)              scaled to size 5.26968pt on input line 113.
LaTeX Font Info:    Trying to load font information for OML+lmm on input line 1
13.
(e:/texlive/2024/texmf-dist/tex/latex/lm/omllmm.fd
File: omllmm.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
)
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(2)/m/n' will be
(Font)              scaled to size 10.54033pt on input line 113.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(2)/m/n' will be
(Font)              scaled to size 7.37823pt on input line 113.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(2)/m/n' will be
(Font)              scaled to size 5.27016pt on input line 113.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(3)/m/n' will be
(Font)              scaled to size 10.53824pt on input line 113.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(3)/m/n' will be
(Font)              scaled to size 7.37677pt on input line 113.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(3)/m/n' will be
(Font)              scaled to size 5.26912pt on input line 113.
LaTeX Font Info:    Trying to load font information for U+msa on input line 113
.

(e:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
(e:/texlive/2024/texmf-dist/tex/latex/microtype/mt-msa.cfg
File: mt-msa.cfg 2006/02/04 v1.1 microtype config. file: AMS symbols (a) (RS)
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 113
.

(e:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
(e:/texlive/2024/texmf-dist/tex/latex/microtype/mt-msb.cfg
File: mt-msb.cfg 2005/06/01 v1.0 microtype config. file: AMS symbols (b) (RS)
)
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(1)/m/n' will be
(Font)              scaled to size 7.52812pt on input line 117.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(1)/m/n' will be
(Font)              scaled to size 5.0pt on input line 117.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(2)/m/n' will be
(Font)              scaled to size 7.52881pt on input line 117.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(2)/m/n' will be
(Font)              scaled to size 5.00046pt on input line 117.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(3)/m/n' will be
(Font)              scaled to size 7.52731pt on input line 117.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(3)/m/n' will be
(Font)              scaled to size 4.99947pt on input line 117.
 [1

] [2] [3]
[4] [5] [6] [7] [8] [9] [10] [11] [12] [13] [14]
Overfull \hbox (29.96036pt too wide) in paragraph at lines 569--580
 [][] 
 []

[15] [16]
Overfull \hbox (5.78304pt too wide) in paragraph at lines 623--634
 [][] 
 []

[17]
Overfull \hbox (130.88052pt too wide) in paragraph at lines 767--778
 [][] 
 []

[18] [19] [20]
Overfull \hbox (140.42433pt too wide) in paragraph at lines 831--866
 [][] 
 []

[21] [22] [23] [24] [25] [26] (./test1.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2022/07/14>
 ***********


LaTeX Font Warning: Some font shapes were not available, defaults substituted.

 ) 
Here is how much of TeX's memory you used:
 22585 strings out of 474773
 459793 string characters out of 5761288
 1947842 words of memory out of 5000000
 44395 multiletter control sequences out of 15000+600000
 565627 words of font info for 99 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 93i,10n,121p,1002b,395s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on test1.pdf (26 pages).
